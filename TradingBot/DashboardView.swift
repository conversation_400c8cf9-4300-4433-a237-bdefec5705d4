//
//  DashboardView.swift
//  TradingBot
//
//  Created by Assistant on 2025-01-20.
//

import SwiftUI

struct DashboardView: View {
    @ObservedObject var databaseManager: DatabaseManager
    @StateObject private var botManager = BotProcessManager()
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 顶部状态栏
                StatusBarView(databaseManager: databaseManager)

                // Bot控制面板
                BotControlPanelView(botManager: botManager)

                // 主要指标卡片
                MetricsCardsView(databaseManager: databaseManager)
                
                // 详细统计
                DetailedStatsView(databaseManager: databaseManager)
                
                // 图表区域（预留）
                ChartSectionView(databaseManager: databaseManager)
            }
            .padding()
        }
        .navigationTitle("交易机器人仪表板")
        .toolbar {
            ToolbarItem(placement: .primaryAction) {
                HStack {
                    // 自动刷新指示器
                    HStack(spacing: 4) {
                        Circle()
                            .fill(Color.green)
                            .frame(width: 6, height: 6)
                            .opacity(0.8)
                        Text("实时")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Button(action: {
                        databaseManager.loadData()
                    }) {
                        Image(systemName: "arrow.clockwise")
                            .foregroundColor(.blue)
                    }
                    .buttonStyle(.borderless)
                    .help("手动刷新数据")

                    Button(action: {
                        databaseManager.selectDatabaseFile()
                    }) {
                        Image(systemName: "folder")
                            .foregroundColor(.orange)
                    }
                    .buttonStyle(.borderless)
                    .help("选择数据库文件")
                }
            }
        }
    }
}

struct StatusBarView: View {
    @ObservedObject var databaseManager: DatabaseManager
    @State private var isBlinking = false

    // 清理和验证symbol文本的函数
    private func cleanSymbolText(_ symbol: String) -> String {
        // 移除可能的控制字符和不可见字符
        let cleaned = symbol.trimmingCharacters(in: .whitespacesAndNewlines)
            .replacingOccurrences(of: "\0", with: "") // 移除null字符
            .filter { $0.isASCII || $0.isLetter || $0.isNumber || $0 == "/" } // 只保留ASCII、字母、数字和斜杠

        // 如果清理后为空或不符合预期格式，返回默认值
        if cleaned.isEmpty || !cleaned.contains("/") {
            return "BTC/USDT"
        }

        return cleaned
    }

    var body: some View {
        HStack {
            // 连接状态 - 添加动画效果
            HStack {
                Circle()
                    .fill(databaseManager.isConnected ? Color.green : Color.red)
                    .frame(width: 10, height: 10)
                    .scaleEffect(isBlinking ? 1.2 : 1.0)
                    .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: isBlinking)
                    .onAppear {
                        if databaseManager.isConnected {
                            isBlinking = true
                        }
                    }
                    .onChange(of: databaseManager.isConnected) { _, connected in
                        isBlinking = connected
                    }

                Text(databaseManager.isConnected ? "已连接" : "未连接")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(databaseManager.isConnected ? .green : .red)
            }

            Spacer()

            // 交易对和价格 - 增强样式
            HStack(spacing: 8) {
                Text(cleanSymbolText(databaseManager.symbol))
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                    .textSelection(.enabled) // 允许选择文本用于调试

                Text(databaseManager.formatCurrency(databaseManager.currentPrice))
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.blue)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(6)
            }

            Spacer()

            // 最后更新时间 - 添加图标
            HStack(spacing: 4) {
                Image(systemName: "clock")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Text("更新: \(databaseManager.lastUpdate, formatter: timeFormatter)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.controlBackgroundColor))
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
    }
    
    private var timeFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.timeStyle = .medium
        return formatter
    }
}

struct MetricsCardsView: View {
    @ObservedObject var databaseManager: DatabaseManager
    
    var body: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
            MetricCard(
                title: "总收益",
                value: databaseManager.formatCurrency(databaseManager.totalProfit),
                subtitle: databaseManager.formatPercentage(databaseManager.profitRate),
                color: databaseManager.totalProfit >= 0 ? .green : .red,
                icon: "dollarsign.circle.fill"
            )
            
            MetricCard(
                title: "当前资金",
                value: databaseManager.formatCurrency(databaseManager.currentFunds),
                subtitle: "初始: \(databaseManager.formatCurrency(databaseManager.initialFunds))",
                color: .blue,
                icon: "banknote.fill"
            )
            
            MetricCard(
                title: "交易次数",
                value: "\(databaseManager.totalTrades)",
                subtitle: "活跃Part: \(databaseManager.activeParts)",
                color: .orange,
                icon: "chart.line.uptrend.xyaxis"
            )
            
            MetricCard(
                title: "小时收益",
                value: databaseManager.formatCurrency(databaseManager.hourlyProfitRate),
                subtitle: "每小时平均",
                color: .purple,
                icon: "clock.fill"
            )
        }
    }
}

struct MetricCard: View {
    let title: String
    let value: String
    let subtitle: String
    let color: Color
    let icon: String
    @State private var isHovered = false

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.title2)
                    .scaleEffect(isHovered ? 1.1 : 1.0)
                    .animation(.easeInOut(duration: 0.2), value: isHovered)
                Spacer()

                // 添加趋势指示器
                Image(systemName: "arrow.up.right")
                    .font(.caption)
                    .foregroundColor(color)
                    .opacity(0.6)
            }

            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
                .textCase(.uppercase)

            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
                .lineLimit(1)
                .minimumScaleFactor(0.8)

            Text(subtitle)
                .font(.caption)
                .foregroundColor(.secondary)
                .lineLimit(1)
        }
        .padding(16)
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.controlBackgroundColor))
                .shadow(color: .black.opacity(0.1), radius: isHovered ? 8 : 4, x: 0, y: isHovered ? 4 : 2)
        )
        .scaleEffect(isHovered ? 1.02 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isHovered)
        .onHover { hovering in
            isHovered = hovering
        }
    }
}

struct DetailedStatsView: View {
    @ObservedObject var databaseManager: DatabaseManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("详细统计")
                .font(.headline)
                .padding(.bottom, 4)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 12) {
                StatItem(title: "平均每笔收益", value: databaseManager.averageProfitPerTrade)
                StatItem(title: "成功率", value: "95.2%") // 示例数据
                StatItem(title: "最大回撤", value: "2.1%") // 示例数据
                StatItem(title: "夏普比率", value: "1.85") // 示例数据
            }
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }
}

struct StatItem: View {
    let title: String
    let value: String
    
    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.title3)
                .fontWeight(.semibold)
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }
}

struct ChartSectionView: View {
    @ObservedObject var databaseManager: DatabaseManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("收益趋势")
                .font(.headline)
            
            // 这里将来可以添加图表
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.controlBackgroundColor))
                .frame(height: 200)
                .overlay(
                    Text("图表区域\n(开发中)")
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                )
        }
    }
}

// MARK: - Bot控制面板
struct BotControlPanelView: View {
    @ObservedObject var botManager: BotProcessManager
    @State private var showingLogs = false

    var body: some View {
        VStack(spacing: 16) {
            // 标题和状态
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Bot控制")
                        .font(.headline)
                        .fontWeight(.semibold)

                    HStack(spacing: 8) {
                        Circle()
                            .fill(statusColor)
                            .frame(width: 8, height: 8)

                        Text(botManager.status.displayText)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                // 日志按钮
                Button(action: { showingLogs.toggle() }) {
                    Image(systemName: "doc.text")
                        .foregroundColor(.blue)
                }
                .help("查看Bot日志")
            }

            // 控制按钮
            HStack(spacing: 12) {
                // 启动按钮
                Button(action: {
                    botManager.startBot()
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: "play.fill")
                        Text("启动")
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                }
                .buttonStyle(.borderedProminent)
                .disabled(botManager.status == .running)

                // 停止按钮
                Button(action: {
                    botManager.stopBot()
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: "stop.fill")
                        Text("停止")
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                }
                .buttonStyle(.bordered)
                .disabled(botManager.status == .stopped)

                // 重启按钮
                Button(action: {
                    botManager.restartBot()
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: "arrow.clockwise")
                        Text("重启")
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                }
                .buttonStyle(.bordered)
                .disabled(botManager.status == .stopped)
            }

            // 快速信息
            if botManager.status == .running {
                HStack {
                    VStack(alignment: .leading, spacing: 2) {
                        Text("运行时间")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                        Text(formatRuntime())
                            .font(.caption)
                            .fontWeight(.medium)
                    }

                    Spacer()

                    VStack(alignment: .trailing, spacing: 2) {
                        Text("状态")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                        Text("活跃")
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                }
                .padding(.top, 4)
            }
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
        .sheet(isPresented: $showingLogs) {
            BotLogsView(botManager: botManager)
        }
    }

    private var statusColor: Color {
        switch botManager.status {
        case .running:
            return .green
        case .stopped:
            return .red
        case .starting:
            return .orange
        case .stopping:
            return .orange
        case .error:
            return .red
        }
    }

    private func formatRuntime() -> String {
        // 这里可以添加运行时间计算逻辑
        return "运行中..."
    }
}

// MARK: - Bot日志视图
struct BotLogsView: View {
    @ObservedObject var botManager: BotProcessManager
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(alignment: .leading, spacing: 4) {
                    ForEach(Array(botManager.logs.enumerated()), id: \.offset) { index, log in
                        Text(log)
                            .font(.system(.caption, design: .monospaced))
                            .padding(.horizontal, 12)
                            .padding(.vertical, 2)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .background(index % 2 == 0 ? Color.clear : Color(.controlBackgroundColor))
                    }
                }
            }
            .navigationTitle("Bot日志")
            .toolbar {
                ToolbarItem(placement: .primaryAction) {
                    Button("关闭") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .secondaryAction) {
                    Button("清除") {
                        botManager.clearLogs()
                    }
                }
            }
        }
        .frame(minWidth: 600, minHeight: 400)
    }
}

// 扩展DatabaseManager以添加计算属性
extension DatabaseManager {
    var averageProfitPerTrade: String {
        guard totalTrades > 0 else { return "$0.00" }
        let average = totalProfit / Double(totalTrades)
        return formatCurrency(average)
    }
}

#Preview {
    DashboardView(databaseManager: DatabaseManager())
}
