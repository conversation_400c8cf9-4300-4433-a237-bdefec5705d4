//
//  BotProcessManager.swift
//  TradingBot
//
//  Created by Assistant on 2025-01-21.
//

import Foundation
import Combine

enum BotStatus: Equatable {
    case stopped
    case starting
    case running
    case stopping
    case error(String)
    
    var displayText: String {
        switch self {
        case .stopped: return "已停止"
        case .starting: return "启动中..."
        case .running: return "运行中"
        case .stopping: return "停止中..."
        case .error(let message): return "错误: \(message)"
        }
    }
    
    var color: String {
        switch self {
        case .stopped: return "gray"
        case .starting: return "orange"
        case .running: return "green"
        case .stopping: return "orange"
        case .error: return "red"
        }
    }
}

class BotProcessManager: ObservableObject {
    @Published var status: BotStatus = .stopped
    @Published var logs: [String] = []
    @Published var isProcessRunning: Bool = false
    @Published var pendingAction: SecurityAction?
    @Published var showingSecurityConfirmation = false

    // 公开Bot目录路径供UI显示
    var botDirectoryPath: String {
        return botDirectory
    }

    private var process: Process?
    private var logPipe: Pipe?
    private var errorPipe: Pipe?
    private var statusCheckTimer: Timer?
    private let securityManager = SecurityManager()
    
    // Bot配置路径
    private let botDirectory: String
    private let botScript = "bot.py"
    private let pythonPath = "/usr/bin/python3"

    private static func getBotDirectory() -> String {
        // 直接返回默认路径，避免文件系统检查阻塞主线程
        // 路径验证将在后台异步进行
        return "/Users/<USER>/my_projects/TradingBot/bot_4.2"
    }

    private func validateBotDirectoryAsync() {
        DispatchQueue.global(qos: .utility).async {
            let candidatePaths = [
                // 相对于应用bundle的路径
                URL(fileURLWithPath: Bundle.main.bundlePath)
                    .deletingLastPathComponent()
                    .deletingLastPathComponent()
                    .deletingLastPathComponent()
                    .appendingPathComponent("bot_4.2")
                    .path,
                // 备用路径
                "/Users/<USER>/my_projects/TradingBot/bot_4.2",
                "/Users/<USER>/bot/bot_4.2",
                "./bot_4.2"
            ]

            var validPath: String?
            for path in candidatePaths {
                if FileManager.default.fileExists(atPath: path) {
                    validPath = path
                    break
                }
            }

            DispatchQueue.main.async {
                if let validPath = validPath, validPath != self.botDirectory {
                    // 如果找到了不同的有效路径，更新它
                    self.addLog("📁 发现Bot目录: \(validPath)")
                } else if validPath == nil {
                    self.addLog("⚠️ Bot目录不存在: \(self.botDirectory)")
                } else {
                    self.addLog("✅ Bot目录验证成功: \(self.botDirectory)")
                }
            }
        }
    }
    
    init() {
        self.botDirectory = Self.getBotDirectory()

        // 完全异步初始化，避免阻塞主线程
        DispatchQueue.main.async {
            self.addLog("🚀 BotProcessManager 初始化完成")
            self.validateBotDirectoryAsync()
            self.startStatusMonitoring()
        }
    }
    
    deinit {
        stopBot()
        statusCheckTimer?.invalidate()
    }
    
    // MARK: - 进程控制
    
    func startBot() {
        requestSecurityConfirmation(for: .startBot) {
            self.performStartBot()
        }
    }

    private func performStartBot() {
        guard status == .stopped else {
            addLog("⚠️ Bot已在运行或正在启动中")
            return
        }

        // 检查是否有其他bot进程在运行
        if checkExistingBotProcess() {
            status = .error("检测到其他Bot进程正在运行，请先停止")
            addLog("❌ 检测到其他Bot进程正在运行")
            return
        }

        status = .starting
        addLog("🚀 开始启动Bot...")

        DispatchQueue.global(qos: .userInitiated).async {
            self.launchBotProcess()
        }
    }
    
    func stopBot() {
        requestSecurityConfirmation(for: .stopBot) {
            self.performStopBot()
        }
    }

    private func performStopBot() {
        guard status == .running || status == .starting else {
            addLog("⚠️ Bot未在运行")
            return
        }

        status = .stopping
        addLog("🛑 正在停止Bot...")

        DispatchQueue.global(qos: .userInitiated).async {
            self.terminateBotProcess()
        }
    }

    func restartBot() {
        requestSecurityConfirmation(for: .restartBot) {
            self.performRestartBot()
        }
    }

    private func performRestartBot() {
        addLog("🔄 重启Bot...")

        if status == .running || status == .starting {
            performStopBot()

            // 等待停止完成后再启动
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                self.performStartBot()
            }
        } else {
            performStartBot()
        }
    }
    
    // MARK: - 进程管理
    
    private func launchBotProcess() {
        do {
            // 创建新进程
            process = Process()
            process?.executableURL = URL(fileURLWithPath: pythonPath)
            process?.arguments = [botScript]
            process?.currentDirectoryURL = URL(fileURLWithPath: botDirectory)
            
            // 设置环境变量
            var environment = ProcessInfo.processInfo.environment
            environment["PYTHONUNBUFFERED"] = "1" // 确保实时输出
            process?.environment = environment
            
            // 设置输出管道
            setupOutputPipes()
            
            // 启动进程
            try process?.run()
            
            DispatchQueue.main.async {
                self.status = .running
                self.isProcessRunning = true
                self.addLog("✅ Bot启动成功")
            }
            
            // 监控进程结束
            process?.terminationHandler = { [weak self] process in
                DispatchQueue.main.async {
                    self?.handleProcessTermination(process)
                }
            }
            
        } catch {
            DispatchQueue.main.async {
                self.status = .error("启动失败: \(error.localizedDescription)")
                self.addLog("❌ Bot启动失败: \(error.localizedDescription)")
            }
        }
    }
    
    private func terminateBotProcess() {
        guard let process = process, process.isRunning else {
            DispatchQueue.main.async {
                self.status = .stopped
                self.isProcessRunning = false
                self.addLog("✅ Bot已停止")
            }
            return
        }
        
        // 优雅停止：先发送SIGTERM
        process.terminate()
        
        // 等待3秒，如果还没停止就强制杀死
        DispatchQueue.global().asyncAfter(deadline: .now() + 3.0) {
            if process.isRunning {
                self.addLog("⚠️ 优雅停止超时，强制终止进程")
                process.interrupt() // SIGKILL
            }
        }
    }
    
    private func handleProcessTermination(_ process: Process) {
        let exitCode = process.terminationStatus
        
        if exitCode == 0 {
            status = .stopped
            addLog("✅ Bot正常退出")
        } else {
            status = .error("进程异常退出，退出码: \(exitCode)")
            addLog("❌ Bot异常退出，退出码: \(exitCode)")
        }
        
        isProcessRunning = false
        self.process = nil
        
        // 清理管道
        logPipe = nil
        errorPipe = nil
    }
    
    // MARK: - 输出处理
    
    private func setupOutputPipes() {
        // 标准输出管道
        logPipe = Pipe()
        process?.standardOutput = logPipe
        
        // 错误输出管道
        errorPipe = Pipe()
        process?.standardError = errorPipe
        
        // 监听输出
        logPipe?.fileHandleForReading.readabilityHandler = { [weak self] handle in
            let data = handle.availableData
            if !data.isEmpty {
                if let output = String(data: data, encoding: .utf8) {
                    DispatchQueue.main.async {
                        self?.addLog(output.trimmingCharacters(in: .whitespacesAndNewlines))
                    }
                }
            }
        }
        
        // 监听错误输出
        errorPipe?.fileHandleForReading.readabilityHandler = { [weak self] handle in
            let data = handle.availableData
            if !data.isEmpty {
                if let output = String(data: data, encoding: .utf8) {
                    DispatchQueue.main.async {
                        self?.addLog("🔴 \(output.trimmingCharacters(in: .whitespacesAndNewlines))")
                    }
                }
            }
        }
    }
    
    // MARK: - 状态监控
    
    private func startStatusMonitoring() {
        // 在主线程上创建定时器，但状态检查在后台执行
        DispatchQueue.main.async { [weak self] in
            self?.statusCheckTimer = Timer.scheduledTimer(withTimeInterval: 10.0, repeats: true) { [weak self] _ in
                // 状态检查在后台线程执行，避免阻塞UI
                DispatchQueue.global(qos: .utility).async {
                    self?.checkBotStatus()
                }
            }
        }
    }
    
    private func checkBotStatus() {
        let isRunning = checkExistingBotProcess()

        // 在主线程上更新状态
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            if isRunning != self.isProcessRunning {
                self.isProcessRunning = isRunning

                if isRunning && self.status == .stopped {
                    self.status = .running
                    self.addLog("🔍 检测到Bot进程正在运行")
                } else if !isRunning && self.status == .running {
                    self.status = .stopped
                    self.addLog("🔍 检测到Bot进程已停止")
                }
            }
        }
    }
    
    private func checkExistingBotProcess() -> Bool {
        let task = Process()
        task.executableURL = URL(fileURLWithPath: "/bin/ps")
        task.arguments = ["aux"]

        let pipe = Pipe()
        task.standardOutput = pipe

        do {
            try task.run()

            // 添加超时机制，避免长时间阻塞
            let semaphore = DispatchSemaphore(value: 0)
            var result = false

            DispatchQueue.global(qos: .utility).async {
                task.waitUntilExit()

                let data = pipe.fileHandleForReading.readDataToEndOfFile()
                if let output = String(data: data, encoding: .utf8) {
                    result = output.contains("python") && output.contains("bot.py")
                }
                semaphore.signal()
            }

            // 等待最多3秒
            if semaphore.wait(timeout: .now() + 3.0) == .timedOut {
                task.terminate()
                print("⚠️ 进程检查超时，已终止")
                return false
            }

            return result
        } catch {
            print("检查进程失败: \(error)")
        }

        return false
    }
    
    // MARK: - 日志管理
    
    private func addLog(_ message: String) {
        // 确保在主线程上更新UI
        DispatchQueue.main.async {
            let timestamp = DateFormatter.logFormatter.string(from: Date())
            let logEntry = "[\(timestamp)] \(message)"

            self.logs.append(logEntry)

            // 限制日志数量，保留最新的1000条
            if self.logs.count > 1000 {
                self.logs.removeFirst(self.logs.count - 1000)
            }
        }
    }
    
    func clearLogs() {
        logs.removeAll()
        addLog("📝 日志已清空")
    }

    // MARK: - 安全确认

    private func requestSecurityConfirmation(for action: SecurityAction, completion: @escaping () -> Void) {
        if securityManager.requiresSecurityCheck(for: action) {
            pendingAction = action
            showingSecurityConfirmation = true

            // 存储完成回调
            pendingCompletion = completion
        } else {
            completion()
        }
    }

    func confirmSecurityAction() {
        pendingCompletion?()
        pendingAction = nil
        showingSecurityConfirmation = false
        pendingCompletion = nil
    }

    func cancelSecurityAction() {
        pendingAction = nil
        showingSecurityConfirmation = false
        pendingCompletion = nil
    }

    func getSecurityWarnings() -> [String] {
        guard let action = pendingAction else { return [] }
        return securityManager.validateAction(action)
    }

    private var pendingCompletion: (() -> Void)?
}

// MARK: - Extensions

extension DateFormatter {
    static let logFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"
        return formatter
    }()
}
