//
//  BinanceAPI.swift
//  TradingBot
//
//  Created by Assistant on 2025-01-21.
//

import Foundation
import CryptoKit

// MARK: - API响应模型
struct BinanceAccountResponse: Codable {
    let balances: [BinanceBalance]
}

struct BinanceBalance: Codable {
    let asset: String
    let free: String
    let locked: String
    
    var totalBalance: Double {
        return (Double(free) ?? 0.0) + (Double(locked) ?? 0.0)
    }
}

struct BinanceTickerResponse: Codable {
    let symbol: String
    let price: String
    let priceChangePercent: String
}

struct BinanceKlineResponse: Codable {
    let openTime: Int64
    let open: String
    let high: String
    let low: String
    let close: String
    let volume: String
    let closeTime: Int64
    let quoteAssetVolume: String
    let numberOfTrades: Int
    let takerBuyBaseAssetVolume: String
    let takerBuyQuoteAssetVolume: String
    let ignore: String
}

// MARK: - Binance API错误
enum BinanceAPIError: Error, LocalizedError {
    case invalidURL
    case noData
    case decodingError(Error)
    case networkError(Error)
    case authenticationError
    case rateLimitExceeded
    case invalidResponse
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的API URL"
        case .noData:
            return "没有返回数据"
        case .decodingError(let error):
            return "数据解析错误: \(error.localizedDescription)"
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        case .authenticationError:
            return "认证失败"
        case .rateLimitExceeded:
            return "API调用频率超限"
        case .invalidResponse:
            return "无效的API响应"
        }
    }
}

// MARK: - Binance API客户端
class BinanceAPI: ObservableObject {
    private let baseURL = "https://api.binance.com"
    private let testnetURL = "https://testnet.binance.vision"
    
    // API凭证 - 从配置文件读取
    private var apiKey: String = ""
    private var secretKey: String = ""
    private var isTestnet: Bool = true // 默认使用测试网
    
    private let session = URLSession.shared
    
    init() {
        loadAPICredentials()
    }
    
    // MARK: - 配置加载
    private func loadAPICredentials() {
        // 从配置文件或环境变量加载API凭证
        // 这里应该从安全的地方读取，比如Keychain
        if let path = Bundle.main.path(forResource: "APIConfig", ofType: "plist"),
           let config = NSDictionary(contentsOfFile: path) {
            apiKey = config["BinanceAPIKey"] as? String ?? ""
            secretKey = config["BinanceSecretKey"] as? String ?? ""
            isTestnet = config["IsTestnet"] as? Bool ?? true
        }
        
        // 如果配置文件不存在，使用默认测试网配置
        if apiKey.isEmpty {
            // 这些是测试网的公开密钥，仅用于演示
            apiKey = "test_api_key"
            secretKey = "test_secret_key"
            isTestnet = true
        }
    }
    
    // MARK: - 公共API方法
    
    /// 获取账户余额
    func getAccountBalances() async throws -> [String: Double] {
        let endpoint = "/api/v3/account"
        let timestamp = String(Int(Date().timeIntervalSince1970 * 1000))
        
        var queryString = "timestamp=\(timestamp)"
        if !isTestnet {
            queryString += "&recvWindow=5000"
        }
        
        let signature = generateSignature(queryString: queryString)
        queryString += "&signature=\(signature)"
        
        let url = "\(currentBaseURL)\(endpoint)?\(queryString)"
        
        let response: BinanceAccountResponse = try await performRequest(url: url, requiresAuth: true)
        
        var balances: [String: Double] = [:]
        for balance in response.balances {
            let total = balance.totalBalance
            if total > 0 {
                balances[balance.asset] = total
            }
        }
        
        return balances
    }
    
    /// 获取当前价格
    func getCurrentPrices() async throws -> [String: Double] {
        let endpoint = "/api/v3/ticker/price"
        let url = "\(currentBaseURL)\(endpoint)"
        
        let response: [BinanceTickerResponse] = try await performRequest(url: url, requiresAuth: false)
        
        var prices: [String: Double] = [:]
        for ticker in response {
            if let price = Double(ticker.price) {
                prices[ticker.symbol] = price
            }
        }
        
        return prices
    }
    
    /// 获取24小时价格变化
    func get24hrPriceChange() async throws -> [String: Double] {
        let endpoint = "/api/v3/ticker/24hr"
        let url = "\(currentBaseURL)\(endpoint)"
        
        let response: [BinanceTickerResponse] = try await performRequest(url: url, requiresAuth: false)
        
        var changes: [String: Double] = [:]
        for ticker in response {
            if let change = Double(ticker.priceChangePercent) {
                changes[ticker.symbol] = change
            }
        }
        
        return changes
    }
    
    /// 获取K线数据
    func getKlineData(symbol: String, interval: String = "1d", limit: Int = 30) async throws -> [BinanceKlineResponse] {
        let endpoint = "/api/v3/klines"
        let queryString = "symbol=\(symbol)&interval=\(interval)&limit=\(limit)"
        let url = "\(currentBaseURL)\(endpoint)?\(queryString)"
        
        let data = try await performRawRequest(url: url)
        
        // Binance K线数据返回的是数组的数组，需要特殊处理
        guard let jsonArray = try JSONSerialization.jsonObject(with: data) as? [[Any]] else {
            throw BinanceAPIError.decodingError(NSError(domain: "BinanceAPI", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid kline data format"]))
        }
        
        var klines: [BinanceKlineResponse] = []
        for item in jsonArray {
            guard item.count >= 12 else { continue }
            
            let kline = BinanceKlineResponse(
                openTime: item[0] as? Int64 ?? 0,
                open: item[1] as? String ?? "0",
                high: item[2] as? String ?? "0",
                low: item[3] as? String ?? "0",
                close: item[4] as? String ?? "0",
                volume: item[5] as? String ?? "0",
                closeTime: item[6] as? Int64 ?? 0,
                quoteAssetVolume: item[7] as? String ?? "0",
                numberOfTrades: item[8] as? Int ?? 0,
                takerBuyBaseAssetVolume: item[9] as? String ?? "0",
                takerBuyQuoteAssetVolume: item[10] as? String ?? "0",
                ignore: item[11] as? String ?? "0"
            )
            klines.append(kline)
        }
        
        return klines
    }
    
    // MARK: - 私有方法
    
    private var currentBaseURL: String {
        return isTestnet ? testnetURL : baseURL
    }
    
    private func performRequest<T: Codable>(url: String, requiresAuth: Bool) async throws -> T {
        let data = try await performRawRequest(url: url, requiresAuth: requiresAuth)
        
        do {
            let decoder = JSONDecoder()
            return try decoder.decode(T.self, from: data)
        } catch {
            throw BinanceAPIError.decodingError(error)
        }
    }
    
    private func performRawRequest(url: String, requiresAuth: Bool = false) async throws -> Data {
        guard let requestURL = URL(string: url) else {
            throw BinanceAPIError.invalidURL
        }
        
        var request = URLRequest(url: requestURL)
        request.httpMethod = "GET"
        
        if requiresAuth {
            request.setValue(apiKey, forHTTPHeaderField: "X-MBX-APIKEY")
        }
        
        do {
            let (data, response) = try await session.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw BinanceAPIError.invalidResponse
            }
            
            switch httpResponse.statusCode {
            case 200:
                return data
            case 429:
                throw BinanceAPIError.rateLimitExceeded
            case 401, 403:
                throw BinanceAPIError.authenticationError
            default:
                throw BinanceAPIError.networkError(NSError(domain: "BinanceAPI", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "HTTP \(httpResponse.statusCode)"]))
            }
        } catch {
            if error is BinanceAPIError {
                throw error
            } else {
                throw BinanceAPIError.networkError(error)
            }
        }
    }
    
    private func generateSignature(queryString: String) -> String {
        guard !secretKey.isEmpty else { return "" }
        
        let key = SymmetricKey(data: secretKey.data(using: .utf8)!)
        let signature = HMAC<SHA256>.authenticationCode(for: queryString.data(using: .utf8)!, using: key)
        return Data(signature).map { String(format: "%02hhx", $0) }.joined()
    }
}

// MARK: - 扩展：模拟数据
extension BinanceAPI {
    /// 获取模拟账户余额（用于演示）
    func getMockAccountBalances() async -> [String: Double] {
        // 模拟网络延迟
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
        
        return [
            "PEPE": 148_004_519.00,
            "FDUSD": 536.********,
            "BTC": 0.********,
            "ETH": 0.********,
            "USDT": 100.0,
            "BNB": 0.5,
            "SOL": 2.5,
            "ADA": 1000.0
        ]
    }
    
    /// 获取模拟价格数据（用于演示）
    func getMockCurrentPrices() async -> [String: Double] {
        // 模拟网络延迟
        try? await Task.sleep(nanoseconds: 300_000_000) // 0.3秒
        
        return [
            "PEPEUSDT": 0.********,
            "FDUSDUSDT": 0.********,
            "BTCUSDT": 100000.00,
            "ETHUSDT": 3333.33,
            "USDTUSDT": 1.0,
            "BNBUSDT": 600.0,
            "SOLUSDT": 150.0,
            "ADAUSDT": 0.45
        ]
    }
}
