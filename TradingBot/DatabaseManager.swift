//
//  DatabaseManager.swift
//  TradingBot
//
//  Created by Assistant on 2025-01-20.
//

import Foundation
import SQLite3
import AppKit

extension String {
    var expandingTildeInPath: String {
        return NSString(string: self).expandingTildeInPath
    }
}

class DatabaseManager: ObservableObject {
    @Published var totalProfit: Double = 0.0
    @Published var currentFunds: Double = 0.0
    @Published var initialFunds: Double = 0.0
    @Published var totalTrades: Int = 0
    @Published var activeParts: Int = 0
    @Published var currentPrice: Double = 0.0
    @Published var symbol: String = "BTC/USDT"
    @Published var lastUpdate: Date = Date()
    @Published var isConnected: Bool = false

    private var db: OpaquePointer?
    private var dbPath: String
    private let dbQueue = DispatchQueue(label: "com.tradingbot.database", qos: .utility)
    
    init() {
        // 初始化数据库路径 - 先设置一个默认值
        self.dbPath = ""

        // 直接连接到实时更新的数据库文件（不使用本地副本）
        let possiblePaths = [
            "/Users/<USER>/my_projects/TradingBot/bot_4.2/data/trading_bot.db",
            "/Users/<USER>/bot/bot_4.2/data/trading_bot.db",
            "~/bot/bot_4.2/data/trading_bot.db".expandingTildeInPath,
            "./bot_4.2/data/trading_bot.db",
            "../bot_4.2/data/trading_bot.db"
        ]

        print("🔍 尝试查找实时数据库文件...")
        for path in possiblePaths {
            print("  检查: \(path)")
            if FileManager.default.fileExists(atPath: path) {
                self.dbPath = path
                print("✅ 找到实时数据库文件: \(path)")
                break
            }
        }

        // 如果复制失败，尝试直接访问项目内的路径
        if dbPath.isEmpty {
            // 获取应用bundle的路径，然后查找相对路径
            let bundlePath = Bundle.main.bundlePath
            let projectRoot = URL(fileURLWithPath: bundlePath)
                .deletingLastPathComponent()
                .deletingLastPathComponent()
                .deletingLastPathComponent()

            let possiblePaths = [
                // 项目内路径（推荐）
                projectRoot.appendingPathComponent("bot_4.2/data/trading_bot.db").path,
                projectRoot.appendingPathComponent("../bot_4.2/data/trading_bot.db").path,

                // 原始路径（备用）
                "/Users/<USER>/bot/bot_4.2/data/trading_bot.db",
                "/Users/<USER>/my_projects/TradingBot/bot_4.2/data/trading_bot.db",
                "~/bot/bot_4.2/data/trading_bot.db".expandingTildeInPath,
                "./bot_4.2/data/trading_bot.db",
                "../bot_4.2/data/trading_bot.db"
            ]

            print("🔍 尝试查找数据库文件...")
            for path in possiblePaths {
                print("  检查: \(path)")
                if FileManager.default.fileExists(atPath: path) {
                    self.dbPath = path
                    print("✅ 找到数据库文件: \(path)")
                    break
                }
            }
        }

        if dbPath.isEmpty {
            print("❌ 未找到数据库文件")
            self.dbPath = "/Users/<USER>/bot/bot_4.2/data/trading_bot.db" // 默认路径
        }

        print("🔍 使用数据库路径: \(dbPath)")

        openDatabase()
        loadData()

        // 设置定时器，每5秒更新一次数据（减少频率避免界面卡顿）
        DispatchQueue.main.async {
            Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { _ in
                // 确保数据加载不阻塞主线程
                DispatchQueue.global(qos: .utility).async {
                    self.loadData()
                }
            }
        }
    }

    private func getDocumentsDirectory() -> URL? {
        return FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first
    }
    
    deinit {
        closeDatabase()
    }
    
    private func openDatabase() {
        // 检查文件是否存在
        if !FileManager.default.fileExists(atPath: dbPath) {
            print("❌ 数据库文件不存在: \(dbPath)")
            DispatchQueue.main.async {
                self.isConnected = false
            }
            return
        }

        if sqlite3_open(dbPath, &db) == SQLITE_OK {
            print("✅ 成功连接到数据库: \(dbPath)")
            DispatchQueue.main.async {
                self.isConnected = true
            }
        } else {
            let errorMessage = String(cString: sqlite3_errmsg(db))
            print("❌ 无法打开数据库: \(dbPath)")
            print("❌ 错误信息: \(errorMessage)")
            DispatchQueue.main.async {
                self.isConnected = false
            }
        }
    }
    
    private func closeDatabase() {
        if sqlite3_close(db) == SQLITE_OK {
            print("✅ 数据库连接已关闭")
        }
    }
    
    func loadData() {
        dbQueue.async {
            // 检查数据库连接状态，如果断开则重新连接
            if !self.isConnected {
                print("🔄 数据库连接断开，尝试重新连接...")
                self.openDatabase()
            }

            self.loadPartFunds()
            self.loadMarketData()
            self.loadProfitHistory()

            DispatchQueue.main.async {
                self.lastUpdate = Date()
            }
        }
    }

    // MARK: - Assets数据获取方法

    func getAssetsData(completion: @escaping ([String: Double]) -> Void) {
        dbQueue.async {
            var assetsData: [String: Double] = [:]

            guard self.db != nil else {
                print("❌ 数据库连接为空")
                DispatchQueue.main.async {
                    completion(assetsData)
                }
                return
            }

            let query = "SELECT assets_data FROM assets WHERE symbol = 'ASSETS' ORDER BY updated_at DESC LIMIT 1"
            var statement: OpaquePointer?

            if sqlite3_prepare_v2(self.db, query, -1, &statement, nil) == SQLITE_OK {
                if sqlite3_step(statement) == SQLITE_ROW {
                    if let assetsJsonCString = sqlite3_column_text(statement, 0) {
                        let assetsJsonString = String(cString: assetsJsonCString)

                        // 解析JSON数据
                        if let jsonData = assetsJsonString.data(using: .utf8) {
                            do {
                                if let jsonObject = try JSONSerialization.jsonObject(with: jsonData) as? [String: [String: Double]] {
                                    // 提取每个币种的total余额
                                    for (asset, balanceInfo) in jsonObject {
                                        assetsData[asset] = balanceInfo["total"] ?? 0.0
                                    }
                                }
                            } catch {
                                print("❌ 解析assets JSON失败: \(error)")
                            }
                        }
                    }
                }
            } else {
                let errorMessage = String(cString: sqlite3_errmsg(self.db))
                print("❌ 查询assets失败: \(errorMessage)")
            }

            sqlite3_finalize(statement)

            DispatchQueue.main.async {
                completion(assetsData)
            }
        }
    }

    func getMarketPrices(completion: @escaping ([String: Double]) -> Void) {
        dbQueue.async {
            var pricesData: [String: Double] = [:]

            guard self.db != nil else {
                print("❌ 数据库连接为空")
                DispatchQueue.main.async {
                    completion(pricesData)
                }
                return
            }

            let query = "SELECT symbol, last_price FROM market_monitor ORDER BY updated_at DESC LIMIT 10"
            var statement: OpaquePointer?

            if sqlite3_prepare_v2(self.db, query, -1, &statement, nil) == SQLITE_OK {
                while sqlite3_step(statement) == SQLITE_ROW {
                    if let symbolCString = sqlite3_column_text(statement, 0) {
                        let symbol = String(cString: symbolCString)
                        let lastPrice = sqlite3_column_double(statement, 1)
                        pricesData[symbol] = lastPrice
                    }
                }
            } else {
                let errorMessage = String(cString: sqlite3_errmsg(self.db))
                print("❌ 查询market_monitor失败: \(errorMessage)")
            }

            sqlite3_finalize(statement)

            DispatchQueue.main.async {
                completion(pricesData)
            }
        }
    }
    
    private func loadPartFunds() {
        guard db != nil else {
            print("❌ 数据库连接为空")
            return
        }

        let query = "SELECT part_name, current_funds, initial_funds, total_profit, trade_count FROM part_funds"
        var statement: OpaquePointer?

        if sqlite3_prepare_v2(db, query, -1, &statement, nil) == SQLITE_OK {
            var allParts: [(name: String, currentFunds: Double, initialFunds: Double, totalProfit: Double, tradeCount: Int)] = []

            print("📊 开始加载part_funds数据...")

            while sqlite3_step(statement) == SQLITE_ROW {
                let partNameCString = sqlite3_column_text(statement, 0)
                let currentFunds = sqlite3_column_double(statement, 1)
                let initialFunds = sqlite3_column_double(statement, 2)
                let totalProfit = sqlite3_column_double(statement, 3)
                let tradeCount = sqlite3_column_int(statement, 4)

                if let partNameCString = partNameCString {
                    let partName = String(cString: partNameCString)
                    print("📋 \(partName): 资金=\(currentFunds), 利润=\(totalProfit), 交易=\(tradeCount)")

                    allParts.append((
                        name: partName,
                        currentFunds: currentFunds,
                        initialFunds: initialFunds,
                        totalProfit: totalProfit,
                        tradeCount: Int(tradeCount)
                    ))
                }
            }

            // 按part名称中的数字排序（与GUI dashboard保持一致）
            allParts.sort { part1, part2 in
                let num1 = extractPartNumber(from: part1.name)
                let num2 = extractPartNumber(from: part2.name)
                return num1 < num2
            }

            // 排除最后一个part进行计算（与GUI dashboard逻辑保持一致）
            let activeParts = allParts.count > 1 ? Array(allParts.dropLast()) : allParts

            var tempCurrentFunds = 0.0
            var tempInitialFunds = 0.0
            var tempTotalProfit = 0.0
            var tempTotalTrades = 0

            for part in activeParts {
                tempCurrentFunds += part.currentFunds
                tempInitialFunds += part.initialFunds
                tempTotalProfit += part.totalProfit
                tempTotalTrades += part.tradeCount
            }

            print("💰 活跃Parts总计: 资金=\(tempCurrentFunds), 利润=\(tempTotalProfit), 交易=\(tempTotalTrades), 活跃Parts=\(activeParts.count)/\(allParts.count)")

            DispatchQueue.main.async {
                self.currentFunds = tempCurrentFunds
                self.initialFunds = tempInitialFunds
                self.totalProfit = tempTotalProfit
                self.totalTrades = tempTotalTrades
                self.activeParts = activeParts.count
            }
        } else {
            let errorMessage = String(cString: sqlite3_errmsg(db))
            print("❌ 查询part_funds失败: \(errorMessage)")
        }

        sqlite3_finalize(statement)
    }

    // 从part名称中提取数字用于排序
    private func extractPartNumber(from partName: String) -> Int {
        let components = partName.components(separatedBy: "_")
        if components.count > 1, let number = Int(components[1]) {
            return number
        }
        return 999 // 如果解析失败，放到最后
    }
    
    private func loadMarketData() {
        guard db != nil else { return }

        let query = "SELECT symbol, last_price FROM market_monitor ORDER BY updated_at DESC LIMIT 1"
        var statement: OpaquePointer?

        if sqlite3_prepare_v2(db, query, -1, &statement, nil) == SQLITE_OK {
            if sqlite3_step(statement) == SQLITE_ROW {
                let symbolCString = sqlite3_column_text(statement, 0)
                let price = sqlite3_column_double(statement, 1)

                print("📊 市场数据: 价格=\(price)")

                // 在SQLite调用的同一线程中处理数据
                var symbolString = "BTC/USDT" // 默认值

                if let symbolCString = symbolCString {
                    // 更安全的字符串创建方式，明确指定UTF-8编码
                    let symbolLength = sqlite3_column_bytes(statement, 0)
                    let symbolData = Data(bytes: symbolCString, count: Int(symbolLength))

                    if let decodedString = String(data: symbolData, encoding: .utf8) {
                        symbolString = decodedString
                        print("📊 成功读取symbol: \(decodedString)")
                    } else {
                        // 如果UTF-8解码失败，使用默认的String(cString:)方法
                        print("⚠️ UTF-8解码失败，使用默认方式")
                        let fallbackSymbol = String(cString: symbolCString)
                        symbolString = fallbackSymbol.isEmpty ? "BTC/USDT" : fallbackSymbol
                        print("📊 使用备用symbol: \(symbolString)")
                    }
                } else {
                    print("⚠️ symbolCString为空，使用默认值")
                }

                // 将结果传递到主线程更新UI
                DispatchQueue.main.async {
                    self.symbol = symbolString
                    self.currentPrice = price
                }
            } else {
                print("⚠️ 未找到市场数据")
                DispatchQueue.main.async {
                    self.symbol = "BTC/USDT" // 设置默认值
                }
            }
        } else {
            let errorMessage = String(cString: sqlite3_errmsg(db))
            print("❌ 查询market_monitor失败: \(errorMessage)")
            DispatchQueue.main.async {
                self.symbol = "BTC/USDT" // 设置默认值
            }
        }

        sqlite3_finalize(statement)
    }
    
    private func loadProfitHistory() {
        // 这里可以加载收益历史数据用于图表显示
        // 暂时先实现基础功能
    }
    
    // 计算收益率
    var profitRate: Double {
        guard initialFunds > 0 else { return 0 }
        return (totalProfit / initialFunds) * 100
    }
    
    // 计算小时收益率（假设运行24小时）
    var hourlyProfitRate: Double {
        return totalProfit / 24.0 // 简化计算，实际应该用真实运行时间
    }
    
    // 格式化货币显示
    func formatCurrency(_ amount: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencySymbol = "$"
        formatter.currencyCode = ""
        return formatter.string(from: NSNumber(value: amount)) ?? "$0.00"
    }
    
    // 格式化百分比显示
    func formatPercentage(_ value: Double) -> String {
        return String(format: "%.2f%%", value)
    }

    // 手动选择数据库文件
    func selectDatabaseFile() {
        let openPanel = NSOpenPanel()
        openPanel.title = "选择交易机器人数据库文件"
        openPanel.allowedContentTypes = [.init(filenameExtension: "db")!]
        openPanel.allowsMultipleSelection = false
        openPanel.canChooseDirectories = false
        openPanel.canChooseFiles = true

        if openPanel.runModal() == .OK {
            if let selectedURL = openPanel.url {
                // 直接使用选择的数据库文件（不复制，实时连接）
                self.dbPath = selectedURL.path
                print("✅ 数据库文件已更新: \(selectedURL.path)")

                // 重新连接数据库
                closeDatabase()
                openDatabase()
                loadData()
            }
        }
    }

    // 强制刷新数据库连接
    func refreshDatabaseConnection() {
        print("🔄 强制刷新数据库连接...")
        closeDatabase()
        openDatabase()
        loadData()
    }
}
