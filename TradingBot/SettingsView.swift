//
//  SettingsView.swift
//  TradingBot
//
//  Created by Assistant on 2025-01-21.
//

import SwiftUI

struct SettingsView: View {
    @StateObject private var settingsManager = SettingsManager()
    @State private var showingResetAlert = false
    @State private var showingValidationAlert = false
    @State private var validationErrors: [String] = []
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 状态栏
                    StatusSection(settingsManager: settingsManager)
                    
                    // 交易设置
                    TradingSection(settingsManager: settingsManager)
                    
                    // 策略设置
                    StrategySection(settingsManager: settingsManager)
                    
                    // 高级设置
                    AdvancedSection(settingsManager: settingsManager)
                    
                    // 操作按钮
                    ActionButtons(
                        settingsManager: settingsManager,
                        showingResetAlert: $showingResetAlert,
                        showingValidationAlert: $showingValidationAlert,
                        validationErrors: $validationErrors
                    )
                }
                .padding()
            }
        }
        .navigationTitle("Bot设置")
        .alert("重置设置", isPresented: $showingResetAlert) {
            Button("取消", role: .cancel) { }
            But<PERSON>("重置", role: .destructive) {
                settingsManager.resetToDefaults()
            }
        } message: {
            Text("确定要重置所有设置为默认值吗？此操作不可撤销。")
        }
        .alert("设置验证", isPresented: $showingValidationAlert) {
            Button("确定") { }
        } message: {
            Text(validationErrors.joined(separator: "\n"))
        }
    }
}

// MARK: - 状态栏

struct StatusSection: View {
    @ObservedObject var settingsManager: SettingsManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("状态")
                .font(.headline)
            
            HStack {
                // 加载状态
                HStack(spacing: 8) {
                    if settingsManager.isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                    }
                    Text(settingsManager.isLoading ? "加载中..." : "已加载")
                        .font(.caption)
                }
                
                Spacer()
                
                // 修改状态
                if settingsManager.hasUnsavedChanges {
                    HStack(spacing: 4) {
                        Circle()
                            .fill(Color.orange)
                            .frame(width: 6, height: 6)
                        Text("有未保存的修改")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }
            }
            
            // 错误信息
            if let error = settingsManager.lastError {
                Text(error)
                    .font(.caption)
                    .foregroundColor(.red)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(4)
            }
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }
}

// MARK: - 交易设置

struct TradingSection: View {
    @ObservedObject var settingsManager: SettingsManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("交易设置")
                .font(.headline)
            
            // 交易对选择
            TradingPairSelector(settingsManager: settingsManager)
            
            // 模拟交易开关
            Toggle("模拟交易", isOn: Binding(
                get: { settingsManager.settings.trading.simulatedTrading },
                set: { settingsManager.updateSimulatedTrading($0) }
            ))
            .help("开启后将使用测试网进行模拟交易")

            // API配置区域 - 简化版本
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("🔑 API配置")
                        .font(.subheadline)
                        .fontWeight(.medium)

                    Spacer()

                    Button("配置") {
                        // 暂时只显示提示
                        print("API配置按钮被点击")
                    }
                    .buttonStyle(.bordered)
                }

                // 直接显示API状态
                HStack {
                    Text("生产环境:")
                        .font(.caption)
                        .frame(width: 70, alignment: .leading)

                    if !settingsManager.settings.trading.api.production.apiKey.isEmpty {
                        Label("已配置", systemImage: "checkmark.circle.fill")
                            .font(.caption)
                            .foregroundColor(.green)
                    } else {
                        Label("未配置", systemImage: "exclamationmark.circle.fill")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }

                    Spacer()
                }

                HStack {
                    Text("测试环境:")
                        .font(.caption)
                        .frame(width: 70, alignment: .leading)

                    if !settingsManager.settings.trading.api.testnet.apiKey.isEmpty {
                        Label("已配置", systemImage: "checkmark.circle.fill")
                            .font(.caption)
                            .foregroundColor(.green)
                    } else {
                        Label("未配置", systemImage: "exclamationmark.circle.fill")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }

                    Spacer()
                }
            }

            // 调试信息 - 临时添加
            Text("调试: API配置已加载 - 生产:\(!settingsManager.settings.trading.api.production.apiKey.isEmpty)")
                .font(.caption)
                .foregroundColor(.blue)
                .padding(.top, 4)
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }
}

// MARK: - 策略设置

struct StrategySection: View {
    @ObservedObject var settingsManager: SettingsManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("策略设置")
                .font(.headline)
            
            // 资金设置
            VStack(alignment: .leading, spacing: 8) {
                Text("Part资金 (USDT)")
                    .font(.subheadline)
                    .fontWeight(.medium)

                VStack(spacing: 12) {
                    // 当前资金
                    HStack {
                        Text("当前资金")
                            .font(.caption)
                            .frame(width: 60, alignment: .leading)

                        Button(action: {
                            let newValue = max(1, settingsManager.settings.strategy.partFunds.current - 10)
                            settingsManager.updatePartFunds(
                                current: newValue,
                                initial: settingsManager.settings.strategy.partFunds.initial
                            )
                        }) {
                            Image(systemName: "minus.circle.fill")
                                .foregroundColor(.red)
                        }
                        .buttonStyle(.plain)

                        TextField("", value: Binding(
                            get: { settingsManager.settings.strategy.partFunds.current },
                            set: { newValue in
                                settingsManager.updatePartFunds(
                                    current: max(1, newValue),
                                    initial: settingsManager.settings.strategy.partFunds.initial
                                )
                            }
                        ), format: .number)
                        .textFieldStyle(.roundedBorder)
                        .frame(width: 80)

                        Button(action: {
                            let newValue = settingsManager.settings.strategy.partFunds.current + 10
                            settingsManager.updatePartFunds(
                                current: newValue,
                                initial: settingsManager.settings.strategy.partFunds.initial
                            )
                        }) {
                            Image(systemName: "plus.circle.fill")
                                .foregroundColor(.green)
                        }
                        .buttonStyle(.plain)

                        Spacer()
                    }

                    // 初始资金
                    HStack {
                        Text("初始资金")
                            .font(.caption)
                            .frame(width: 60, alignment: .leading)

                        Button(action: {
                            let newValue = max(1, settingsManager.settings.strategy.partFunds.initial - 10)
                            settingsManager.updatePartFunds(
                                current: settingsManager.settings.strategy.partFunds.current,
                                initial: newValue
                            )
                        }) {
                            Image(systemName: "minus.circle.fill")
                                .foregroundColor(.red)
                        }
                        .buttonStyle(.plain)

                        TextField("", value: Binding(
                            get: { settingsManager.settings.strategy.partFunds.initial },
                            set: { newValue in
                                settingsManager.updatePartFunds(
                                    current: settingsManager.settings.strategy.partFunds.current,
                                    initial: max(1, newValue)
                                )
                            }
                        ), format: .number)
                        .textFieldStyle(.roundedBorder)
                        .frame(width: 80)

                        Button(action: {
                            let newValue = settingsManager.settings.strategy.partFunds.initial + 10
                            settingsManager.updatePartFunds(
                                current: settingsManager.settings.strategy.partFunds.current,
                                initial: newValue
                            )
                        }) {
                            Image(systemName: "plus.circle.fill")
                                .foregroundColor(.green)
                        }
                        .buttonStyle(.plain)

                        Spacer()
                    }
                }
            }
            
            // 订单数量
            VStack(alignment: .leading, spacing: 8) {
                Text("每Part订单数量")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Stepper(value: Binding(
                    get: { settingsManager.settings.strategy.partOrdersCount },
                    set: { settingsManager.settings.strategy.partOrdersCount = $0; settingsManager.hasUnsavedChanges = true }
                ), in: 1...10) {
                    Text("\(settingsManager.settings.strategy.partOrdersCount) 个订单")
                }
            }
            
            // 止盈率
            VStack(alignment: .leading, spacing: 8) {
                Text("止盈率")
                    .font(.subheadline)
                    .fontWeight(.medium)

                HStack {
                    Button(action: {
                        let newValue = max(1.001, settingsManager.settings.strategy.tpRate - 0.001)
                        settingsManager.updateTpRate(newValue)
                    }) {
                        Image(systemName: "minus.circle.fill")
                            .foregroundColor(.red)
                    }
                    .buttonStyle(.plain)

                    TextField("", value: Binding(
                        get: { settingsManager.settings.strategy.tpRate },
                        set: { newValue in
                            let clampedValue = max(1.001, min(1.1, newValue))
                            settingsManager.updateTpRate(clampedValue)
                        }
                    ), format: .number.precision(.fractionLength(3)))
                    .textFieldStyle(.roundedBorder)
                    .frame(width: 80)

                    Button(action: {
                        let newValue = min(1.1, settingsManager.settings.strategy.tpRate + 0.001)
                        settingsManager.updateTpRate(newValue)
                    }) {
                        Image(systemName: "plus.circle.fill")
                            .foregroundColor(.green)
                    }
                    .buttonStyle(.plain)

                    Text(String(format: "%.1f%%", (settingsManager.settings.strategy.tpRate - 1) * 100))
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .frame(width: 50)

                    Spacer()
                }
            }
            
            // 合并间隔
            VStack(alignment: .leading, spacing: 8) {
                Text("合并间隔")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Stepper(value: Binding(
                    get: { settingsManager.settings.strategy.mergeInterval },
                    set: { settingsManager.settings.strategy.mergeInterval = $0; settingsManager.hasUnsavedChanges = true }
                ), in: 1...100) {
                    Text("\(settingsManager.settings.strategy.mergeInterval) 秒")
                }
            }
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }
}

// MARK: - 高级设置

struct AdvancedSection: View {
    @ObservedObject var settingsManager: SettingsManager
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Button(action: { isExpanded.toggle() }) {
                HStack {
                    Text("高级设置")
                        .font(.headline)
                    Spacer()
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .foregroundColor(.secondary)
                }
            }
            .buttonStyle(.plain)
            
            if isExpanded {
                // 循环间隔
                VStack(alignment: .leading, spacing: 8) {
                    Text("循环间隔 (秒)")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Stepper(value: Binding(
                        get: { settingsManager.settings.timing.loopIntervalSeconds },
                        set: { settingsManager.updateLoopInterval($0) }
                    ), in: 1...60) {
                        Text("\(settingsManager.settings.timing.loopIntervalSeconds) 秒")
                    }
                }
                
                // 价格保护
                Toggle("启用价格保护", isOn: Binding(
                    get: { settingsManager.settings.strategy.priceProtection.enable },
                    set: { settingsManager.settings.strategy.priceProtection.enable = $0; settingsManager.hasUnsavedChanges = true }
                ))
                
                // 价格梯度
                Toggle("启用价格梯度", isOn: Binding(
                    get: { settingsManager.settings.strategy.priceGradient.enable },
                    set: { settingsManager.settings.strategy.priceGradient.enable = $0; settingsManager.hasUnsavedChanges = true }
                ))
                
                if settingsManager.settings.strategy.priceGradient.enable {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("梯度步长")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        TextField("步长", value: Binding(
                            get: { settingsManager.settings.strategy.priceGradient.stepSize },
                            set: { settingsManager.settings.strategy.priceGradient.stepSize = $0; settingsManager.hasUnsavedChanges = true }
                        ), format: .number)
                        .textFieldStyle(.roundedBorder)
                    }
                }
            }
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }
}

// MARK: - 操作按钮

struct ActionButtons: View {
    @ObservedObject var settingsManager: SettingsManager
    @Binding var showingResetAlert: Bool
    @Binding var showingValidationAlert: Bool
    @Binding var validationErrors: [String]
    
    var body: some View {
        HStack(spacing: 12) {
            // 重置按钮
            Button("重置为默认") {
                showingResetAlert = true
            }
            .buttonStyle(.bordered)
            
            // 取消修改按钮
            Button("取消修改") {
                settingsManager.discardChanges()
            }
            .buttonStyle(.bordered)
            .disabled(!settingsManager.hasUnsavedChanges)
            
            Spacer()
            
            // 保存按钮
            Button("保存设置") {
                let errors = settingsManager.validateSettings()
                if errors.isEmpty {
                    settingsManager.saveSettings()
                } else {
                    validationErrors = errors
                    showingValidationAlert = true
                }
            }
            .buttonStyle(.borderedProminent)
            .disabled(!settingsManager.hasUnsavedChanges || settingsManager.isLoading)
        }
    }
}

// MARK: - 交易对选择器组件
struct TradingPairSelector: View {
    @ObservedObject var settingsManager: SettingsManager
    @State private var searchText = ""
    @State private var isExpanded = false

    var filteredSymbols: [String] {
        if searchText.isEmpty {
            return settingsManager.supportedSymbols
        } else {
            return settingsManager.supportedSymbols.filter { symbol in
                symbol.localizedCaseInsensitiveContains(searchText)
            }
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("交易对")
                .font(.subheadline)
                .fontWeight(.medium)

            VStack(spacing: 0) {
                // 当前选择显示
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        isExpanded.toggle()
                    }
                }) {
                    HStack {
                        Text(settingsManager.settings.trading.symbol)
                            .foregroundColor(.primary)

                        Spacer()

                        Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                            .foregroundColor(.secondary)
                            .rotationEffect(.degrees(isExpanded ? 180 : 0))
                            .animation(.easeInOut(duration: 0.2), value: isExpanded)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color(NSColor.controlBackgroundColor))
                    .cornerRadius(6)
                    .overlay(
                        RoundedRectangle(cornerRadius: 6)
                            .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                    )
                }
                .buttonStyle(.plain)

                // 下拉列表
                if isExpanded {
                    VStack(spacing: 0) {
                        // 搜索框
                        HStack {
                            Image(systemName: "magnifyingglass")
                                .foregroundColor(.secondary)

                            TextField("搜索交易对...", text: $searchText)
                                .textFieldStyle(.plain)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color(NSColor.textBackgroundColor))

                        Divider()

                        // 选项列表
                        ScrollView {
                            LazyVStack(spacing: 0) {
                                ForEach(filteredSymbols, id: \.self) { symbol in
                                    Button(action: {
                                        settingsManager.updateSymbol(symbol)
                                        withAnimation(.easeInOut(duration: 0.2)) {
                                            isExpanded = false
                                        }
                                        searchText = ""
                                    }) {
                                        HStack {
                                            Text(symbol)
                                                .foregroundColor(.primary)

                                            Spacer()

                                            if symbol == settingsManager.settings.trading.symbol {
                                                Image(systemName: "checkmark")
                                                    .foregroundColor(.blue)
                                            }
                                        }
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 6)
                                        .background(
                                            symbol == settingsManager.settings.trading.symbol ?
                                            Color.blue.opacity(0.1) : Color.clear
                                        )
                                    }
                                    .buttonStyle(.plain)
                                    .onHover { isHovered in
                                        // 可以添加悬停效果
                                    }
                                }
                            }
                        }
                        .frame(maxHeight: 200)
                        .background(Color(NSColor.textBackgroundColor))
                    }
                    .background(Color(NSColor.textBackgroundColor))
                    .cornerRadius(6)
                    .overlay(
                        RoundedRectangle(cornerRadius: 6)
                            .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                    )
                    .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                }
            }
        }
    }
}

// MARK: - API配置组件

struct APIConfigurationSection: View {
    @ObservedObject var settingsManager: SettingsManager
    @State private var showingAPIConfig = false

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 调试信息
            Text("DEBUG: APIConfigurationSection 正在渲染")
                .font(.caption)
                .foregroundColor(.red)

            HStack {
                Text("🔑 API配置")
                    .font(.subheadline)
                    .fontWeight(.medium)

                Spacer()

                Button("配置") {
                    showingAPIConfig = true
                }
                .buttonStyle(.bordered)
            }

            // API状态显示
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text("生产环境:")
                        .font(.caption)
                        .frame(width: 70, alignment: .leading)

                    if !settingsManager.settings.trading.api.production.apiKey.isEmpty {
                        Label("已配置", systemImage: "checkmark.circle.fill")
                            .font(.caption)
                            .foregroundColor(.green)
                    } else {
                        Label("未配置", systemImage: "exclamationmark.circle.fill")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }

                    Spacer()
                }

                HStack {
                    Text("测试环境:")
                        .font(.caption)
                        .frame(width: 70, alignment: .leading)

                    if !settingsManager.settings.trading.api.testnet.apiKey.isEmpty {
                        Label("已配置", systemImage: "checkmark.circle.fill")
                            .font(.caption)
                            .foregroundColor(.green)
                    } else {
                        Label("未配置", systemImage: "exclamationmark.circle.fill")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }

                    Spacer()
                }
            }
        }
        .sheet(isPresented: $showingAPIConfig) {
            APIConfigurationDetailView(settingsManager: settingsManager)
        }
    }
}

// MARK: - API配置详细界面

struct APIConfigurationDetailView: View {
    @ObservedObject var settingsManager: SettingsManager
    @Environment(\.dismiss) private var dismiss

    @State private var productionAPIKey: String = ""
    @State private var productionSecretKey: String = ""
    @State private var testnetAPIKey: String = ""
    @State private var testnetSecretKey: String = ""
    @State private var autoSelectByMode: Bool = true

    @State private var showProductionAPI = false
    @State private var showProductionSecret = false
    @State private var showTestnetAPI = false
    @State private var showTestnetSecret = false

    @State private var validationErrors: [String] = []
    @State private var showingValidationAlert = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 当前模式指示
                    CurrentModeIndicator(settingsManager: settingsManager)

                    // 生产环境API配置
                    APICredentialsInputSection(
                        title: "🏭 生产环境API (Binance HK)",
                        apiKey: $productionAPIKey,
                        secretKey: $productionSecretKey,
                        showAPIKey: $showProductionAPI,
                        showSecretKey: $showProductionSecret,
                        isActive: !settingsManager.settings.trading.simulatedTrading
                    )

                    // 测试环境API配置
                    APICredentialsInputSection(
                        title: "🧪 测试环境API (Binance Testnet)",
                        apiKey: $testnetAPIKey,
                        secretKey: $testnetSecretKey,
                        showAPIKey: $showTestnetAPI,
                        showSecretKey: $showTestnetSecret,
                        isActive: settingsManager.settings.trading.simulatedTrading
                    )

                    // 高级设置
                    VStack(alignment: .leading, spacing: 12) {
                        Text("⚙️ 高级设置")
                            .font(.headline)

                        Toggle("根据交易模式自动选择API", isOn: $autoSelectByMode)
                            .help("开启后会根据模拟交易开关自动选择使用的API")
                    }
                    .padding()
                    .background(Color(.controlBackgroundColor))
                    .cornerRadius(12)
                }
                .padding()
            }
            .navigationTitle("API配置")
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("取消") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .confirmationAction) {
                    Button("保存") {
                        saveAPIConfiguration()
                    }
                    .buttonStyle(.borderedProminent)
                }
            }
        }
        .onAppear {
            loadCurrentSettings()
        }
        .alert("配置验证", isPresented: $showingValidationAlert) {
            Button("确定") { }
        } message: {
            Text(validationErrors.joined(separator: "\n"))
        }
    }

    private func loadCurrentSettings() {
        productionAPIKey = settingsManager.settings.trading.api.production.apiKey
        productionSecretKey = settingsManager.settings.trading.api.production.secretKey
        testnetAPIKey = settingsManager.settings.trading.api.testnet.apiKey
        testnetSecretKey = settingsManager.settings.trading.api.testnet.secretKey
        autoSelectByMode = settingsManager.settings.trading.api.autoSelectByMode
    }

    private func saveAPIConfiguration() {
        // 验证输入
        var errors: [String] = []

        if !productionAPIKey.isEmpty && productionAPIKey.count != 64 {
            errors.append("生产环境API Key长度必须为64位")
        }
        if !productionSecretKey.isEmpty && productionSecretKey.count != 64 {
            errors.append("生产环境Secret Key长度必须为64位")
        }
        if !testnetAPIKey.isEmpty && testnetAPIKey.count != 64 {
            errors.append("测试环境API Key长度必须为64位")
        }
        if !testnetSecretKey.isEmpty && testnetSecretKey.count != 64 {
            errors.append("测试环境Secret Key长度必须为64位")
        }

        if !errors.isEmpty {
            validationErrors = errors
            showingValidationAlert = true
            return
        }

        // 保存配置
        let productionCredentials = APICredentials(
            apiKey: productionAPIKey,
            secretKey: productionSecretKey,
            description: "Binance HK Production API"
        )

        let testnetCredentials = APICredentials(
            apiKey: testnetAPIKey,
            secretKey: testnetSecretKey,
            description: "Binance Testnet API"
        )

        settingsManager.updateAPICredentials(
            production: productionCredentials,
            testnet: testnetCredentials
        )
        settingsManager.updateAutoSelectAPI(autoSelectByMode)

        dismiss()
    }
}

// MARK: - 辅助组件

struct CurrentModeIndicator: View {
    @ObservedObject var settingsManager: SettingsManager

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("当前交易模式")
                .font(.headline)

            HStack {
                if settingsManager.settings.trading.simulatedTrading {
                    Label("模拟交易模式", systemImage: "testtube.2")
                        .foregroundColor(.blue)
                } else {
                    Label("实盘交易模式", systemImage: "dollarsign.circle")
                        .foregroundColor(.green)
                }

                Spacer()
            }

            Text(settingsManager.settings.trading.simulatedTrading ?
                 "当前使用测试环境API进行模拟交易" :
                 "当前使用生产环境API进行实盘交易")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }
}

struct APICredentialsInputSection: View {
    let title: String
    @Binding var apiKey: String
    @Binding var secretKey: String
    @Binding var showAPIKey: Bool
    @Binding var showSecretKey: Bool
    let isActive: Bool

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text(title)
                    .font(.headline)

                Spacer()

                if isActive {
                    Label("当前使用", systemImage: "checkmark.circle.fill")
                        .font(.caption)
                        .foregroundColor(.green)
                }
            }

            // API Key输入
            VStack(alignment: .leading, spacing: 4) {
                Text("API Key")
                    .font(.subheadline)
                    .fontWeight(.medium)

                HStack {
                    if showAPIKey {
                        TextField("请输入64位API Key", text: $apiKey)
                            .textFieldStyle(.roundedBorder)
                            .font(.system(.body, design: .monospaced))
                    } else {
                        SecureField("请输入64位API Key", text: $apiKey)
                            .textFieldStyle(.roundedBorder)
                            .font(.system(.body, design: .monospaced))
                    }

                    Button(showAPIKey ? "隐藏" : "显示") {
                        showAPIKey.toggle()
                    }
                    .buttonStyle(.bordered)
                }

                if !apiKey.isEmpty {
                    Text("长度: \(apiKey.count)/64")
                        .font(.caption)
                        .foregroundColor(apiKey.count == 64 ? .green : .orange)
                }
            }

            // Secret Key输入
            VStack(alignment: .leading, spacing: 4) {
                Text("Secret Key")
                    .font(.subheadline)
                    .fontWeight(.medium)

                HStack {
                    if showSecretKey {
                        TextField("请输入64位Secret Key", text: $secretKey)
                            .textFieldStyle(.roundedBorder)
                            .font(.system(.body, design: .monospaced))
                    } else {
                        SecureField("请输入64位Secret Key", text: $secretKey)
                            .textFieldStyle(.roundedBorder)
                            .font(.system(.body, design: .monospaced))
                    }

                    Button(showSecretKey ? "隐藏" : "显示") {
                        showSecretKey.toggle()
                    }
                    .buttonStyle(.bordered)
                }

                if !secretKey.isEmpty {
                    Text("长度: \(secretKey.count)/64")
                        .font(.caption)
                        .foregroundColor(secretKey.count == 64 ? .green : .orange)
                }
            }
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
        .opacity(isActive ? 1.0 : 0.7)
    }
}

#Preview {
    SettingsView()
}
