//
//  SettingsManager.swift
//  TradingBot
//
//  Created by Assistant on 2025-01-21.
//

import Foundation
import Combine

// MARK: - 数据模型

struct APICredentials: Codable {
    var apiKey: String
    var secretKey: String
    var description: String

    enum CodingKeys: String, CodingKey {
        case apiKey = "api_key"
        case secretKey = "secret_key"
        case description
    }
}

struct APIConfiguration: Codable {
    var production: APICredentials
    var testnet: APICredentials
    var autoSelectByMode: Bool

    enum CodingKeys: String, CodingKey {
        case production
        case testnet
        case autoSelectByMode = "auto_select_by_mode"
    }
}

struct TradingSettings: Codable {
    var symbol: String
    var simulatedTrading: Bool
    var api: APIConfiguration

    enum CodingKeys: String, CodingKey {
        case symbol
        case simulatedTrading = "simulated_trading"
        case api
    }
}

struct MarketSettings: Codable {
    var tickSize: Double
    var minPriceDiffMultiplier: Int
    
    enum CodingKeys: String, CodingKey {
        case tickSize = "tick_size"
        case minPriceDiffMultiplier = "min_price_diff_multiplier"
    }
}

struct PartFunds: Codable {
    var current: Double
    var initial: Double
}

struct PriceProtection: Codable {
    var enable: Bool
    var minPriceGapTicks: Int
    var minProfitRate: Double
    
    enum CodingKeys: String, CodingKey {
        case enable
        case minPriceGapTicks = "min_price_gap_ticks"
        case minProfitRate = "min_profit_rate"
    }
}

struct PriceGradient: Codable {
    var enable: Bool
    var stepSize: Double
    var intraPartStep: Double
    var interPartStep: Double
    
    enum CodingKeys: String, CodingKey {
        case enable
        case stepSize = "step_size"
        case intraPartStep = "intra_part_step"
        case interPartStep = "inter_part_step"
    }
}

struct StrategySettings: Codable {
    var partFunds: PartFunds
    var partOrdersCount: Int
    var tpRate: Double
    var mergeInterval: Int
    var priceProtection: PriceProtection
    var priceGradient: PriceGradient
    
    enum CodingKeys: String, CodingKey {
        case partFunds = "part_funds"
        case partOrdersCount = "part_orders_count"
        case tpRate = "tp_rate"
        case mergeInterval = "merge_interval"
        case priceProtection = "price_protection"
        case priceGradient = "price_gradient"
    }
}

struct TimingSettings: Codable {
    var loopIntervalSeconds: Int
    
    enum CodingKeys: String, CodingKey {
        case loopIntervalSeconds = "loop_interval_seconds"
    }
}

struct PerformanceSettings: Codable {
    var enableMonitoring: Bool
    var logSlowQueries: Bool
    var slowQueryThresholdMs: Int
    
    enum CodingKeys: String, CodingKey {
        case enableMonitoring = "enable_monitoring"
        case logSlowQueries = "log_slow_queries"
        case slowQueryThresholdMs = "slow_query_threshold_ms"
    }
}

struct StorageSettings: Codable {
    var mode: String
    var sqliteDbPath: String
    var performance: PerformanceSettings
    
    enum CodingKeys: String, CodingKey {
        case mode
        case sqliteDbPath = "sqlite_db_path"
        case performance
    }
}

struct BotSettings: Codable {
    var trading: TradingSettings
    var market: MarketSettings
    var strategy: StrategySettings
    var timing: TimingSettings
    var storage: StorageSettings
}

// MARK: - 设置管理器

class SettingsManager: ObservableObject {
    @Published var settings: BotSettings
    @Published var isLoading: Bool = false
    @Published var lastError: String?
    @Published var hasUnsavedChanges: Bool = false
    
    private let settingsPath: String
    private var originalSettings: BotSettings
    
    // 支持的交易对列表
    let supportedSymbols = [
        // 主流币种
        "BTC/USDT", "ETH/USDT", "BNB/USDT", "XRP/USDT", "ADA/USDT",
        "SOL/USDT", "DOGE/USDT", "DOT/USDT", "AVAX/USDT", "MATIC/USDT",
        "LINK/USDT", "UNI/USDT", "LTC/USDT", "ATOM/USDT", "FTM/USDT",

        // 热门DeFi币种
        "AAVE/USDT", "COMP/USDT", "SUSHI/USDT", "CRV/USDT", "YFI/USDT",
        "1INCH/USDT", "MKR/USDT", "SNX/USDT", "BAL/USDT", "REN/USDT",

        // Layer 2 & 新兴币种
        "NEAR/USDT", "ALGO/USDT", "VET/USDT", "ICP/USDT", "FIL/USDT",
        "THETA/USDT", "EOS/USDT", "TRX/USDT", "XLM/USDT", "HBAR/USDT",

        // Meme币种
        "SHIB/USDT", "PEPE/USDT", "FLOKI/USDT", "BONK/USDT",

        // 其他热门币种
        "APT/USDT", "SUI/USDT", "ARB/USDT", "OP/USDT", "LDO/USDT",
        "RNDR/USDT", "GRT/USDT", "SAND/USDT", "MANA/USDT", "AXS/USDT",
        "GMT/USDT", "APE/USDT", "GALA/USDT", "CHZ/USDT", "ENJ/USDT"
    ]
    
    init() {
        // 动态获取设置路径
        self.settingsPath = Self.getSettingsPath()

        // 创建默认设置
        let defaultSettings = Self.createDefaultSettings()
        self.settings = defaultSettings
        self.originalSettings = defaultSettings

        // 加载现有设置
        loadSettings()
    }
    
    // MARK: - 文件操作
    
    func loadSettings() {
        isLoading = true
        lastError = nil
        
        DispatchQueue.global(qos: .userInitiated).async {
            do {
                let data = try Data(contentsOf: URL(fileURLWithPath: self.settingsPath))
                let loadedSettings = try JSONDecoder().decode(BotSettings.self, from: data)
                
                DispatchQueue.main.async {
                    self.settings = loadedSettings
                    self.originalSettings = loadedSettings
                    self.hasUnsavedChanges = false
                    self.isLoading = false
                    print("DEBUG: 设置加载成功")
                    print("DEBUG: API配置存在 - 生产: \(!loadedSettings.trading.api.production.apiKey.isEmpty)")
                    print("DEBUG: API配置存在 - 测试: \(!loadedSettings.trading.api.testnet.apiKey.isEmpty)")
                }
            } catch {
                DispatchQueue.main.async {
                    self.lastError = "加载设置失败: \(error.localizedDescription)"
                    self.isLoading = false
                    print("DEBUG: 设置加载失败 - \(error)")
                    print("DEBUG: 使用默认设置")
                }
            }
        }
    }
    
    func saveSettings() {
        isLoading = true
        lastError = nil
        
        DispatchQueue.global(qos: .userInitiated).async {
            do {
                // 创建备份
                self.createBackupInternal()
                
                // 保存新设置
                let encoder = JSONEncoder()
                encoder.outputFormatting = [.prettyPrinted, .sortedKeys, .withoutEscapingSlashes]
                let data = try encoder.encode(self.settings)
                
                try data.write(to: URL(fileURLWithPath: self.settingsPath))
                
                DispatchQueue.main.async {
                    self.originalSettings = self.settings
                    self.hasUnsavedChanges = false
                    self.isLoading = false
                }
            } catch {
                DispatchQueue.main.async {
                    self.lastError = "保存设置失败: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }
    
    func resetToDefaults() {
        settings = Self.createDefaultSettings()
        hasUnsavedChanges = true
    }
    
    func discardChanges() {
        settings = originalSettings
        hasUnsavedChanges = false
    }
    
    // MARK: - 备份管理

    func createBackup() throws -> String {
        let backupDir = "/Users/<USER>/my_projects/TradingBot/bot_4.2/backup"
        let timestamp = DateFormatter.backupFormatter.string(from: Date())
        let backupPath = "\(backupDir)/settings_backup_\(timestamp).json"

        // 确保备份目录存在
        try FileManager.default.createDirectory(atPath: backupDir, withIntermediateDirectories: true)

        // 复制当前设置文件
        try FileManager.default.copyItem(atPath: settingsPath, toPath: backupPath)

        return backupPath
    }

    private func createBackupInternal() {
        do {
            _ = try createBackup()
        } catch {
            print("创建备份失败: \(error)")
        }
    }
    
    // MARK: - 设置验证
    
    func validateSettings() -> [String] {
        var errors: [String] = []
        
        // 验证交易对
        if !supportedSymbols.contains(settings.trading.symbol) {
            errors.append("不支持的交易对: \(settings.trading.symbol)")
        }
        
        // 验证资金设置
        if settings.strategy.partFunds.current <= 0 {
            errors.append("当前资金必须大于0")
        }
        
        if settings.strategy.partFunds.initial <= 0 {
            errors.append("初始资金必须大于0")
        }
        
        // 验证订单数量
        if settings.strategy.partOrdersCount < 1 || settings.strategy.partOrdersCount > 10 {
            errors.append("订单数量必须在1-10之间")
        }
        
        // 验证止盈率
        if settings.strategy.tpRate <= 1.0 || settings.strategy.tpRate > 2.0 {
            errors.append("止盈率必须在1.0-2.0之间")
        }
        
        // 验证循环间隔
        if settings.timing.loopIntervalSeconds < 1 || settings.timing.loopIntervalSeconds > 60 {
            errors.append("循环间隔必须在1-60秒之间")
        }
        
        return errors
    }
    
    // MARK: - 便捷方法
    
    func updateSymbol(_ symbol: String) {
        settings.trading.symbol = symbol
        hasUnsavedChanges = true
    }
    
    func updateSimulatedTrading(_ enabled: Bool) {
        settings.trading.simulatedTrading = enabled
        hasUnsavedChanges = true
    }
    
    func updatePartFunds(current: Double, initial: Double) {
        settings.strategy.partFunds.current = current
        settings.strategy.partFunds.initial = initial
        hasUnsavedChanges = true
    }
    
    func updateTpRate(_ rate: Double) {
        settings.strategy.tpRate = rate
        hasUnsavedChanges = true
    }
    
    func updateLoopInterval(_ seconds: Int) {
        settings.timing.loopIntervalSeconds = seconds
        hasUnsavedChanges = true
    }

    // MARK: - API配置方法

    func updateAPICredentials(production: APICredentials?, testnet: APICredentials?) {
        if let production = production {
            settings.trading.api.production = production
        }
        if let testnet = testnet {
            settings.trading.api.testnet = testnet
        }
        hasUnsavedChanges = true
    }

    func updateAutoSelectAPI(_ enabled: Bool) {
        settings.trading.api.autoSelectByMode = enabled
        hasUnsavedChanges = true
    }

    func getCurrentAPICredentials() -> APICredentials {
        return settings.trading.simulatedTrading ? settings.trading.api.testnet : settings.trading.api.production
    }

    func validateAPICredentials() -> [String] {
        var errors: [String] = []

        // 验证生产环境API
        let production = settings.trading.api.production
        if production.apiKey.isEmpty {
            errors.append("生产环境API Key不能为空")
        } else if production.apiKey.count != 64 {
            errors.append("生产环境API Key长度必须为64位")
        }

        if production.secretKey.isEmpty {
            errors.append("生产环境Secret Key不能为空")
        } else if production.secretKey.count != 64 {
            errors.append("生产环境Secret Key长度必须为64位")
        }

        // 验证测试环境API
        let testnet = settings.trading.api.testnet
        if testnet.apiKey.isEmpty {
            errors.append("测试环境API Key不能为空")
        } else if testnet.apiKey.count != 64 {
            errors.append("测试环境API Key长度必须为64位")
        }

        if testnet.secretKey.isEmpty {
            errors.append("测试环境Secret Key不能为空")
        } else if testnet.secretKey.count != 64 {
            errors.append("测试环境Secret Key长度必须为64位")
        }

        return errors
    }
    
    // MARK: - 路径管理

    private static func getSettingsPath() -> String {
        // 首先尝试相对于应用bundle的路径
        let bundlePath = Bundle.main.bundlePath
        let relativePath = URL(fileURLWithPath: bundlePath)
            .deletingLastPathComponent()
            .deletingLastPathComponent()
            .deletingLastPathComponent()
            .appendingPathComponent("bot_4.2")
            .appendingPathComponent("settings.json")
            .path

        if FileManager.default.fileExists(atPath: relativePath) {
            return relativePath
        }

        // 备用路径
        let fallbackPaths = [
            "/Users/<USER>/my_projects/TradingBot/bot_4.2/settings.json",
            "/Users/<USER>/bot/bot_4.2/settings.json",
            "./bot_4.2/settings.json"
        ]

        for path in fallbackPaths {
            if FileManager.default.fileExists(atPath: path) {
                return path
            }
        }

        // 默认路径
        return "/Users/<USER>/my_projects/TradingBot/bot_4.2/settings.json"
    }

    // MARK: - 默认设置

    private static func createDefaultSettings() -> BotSettings {
        return BotSettings(
            trading: TradingSettings(
                symbol: "BTC/USDT",
                simulatedTrading: true,
                api: APIConfiguration(
                    production: APICredentials(
                        apiKey: "",
                        secretKey: "",
                        description: "Binance HK Production API"
                    ),
                    testnet: APICredentials(
                        apiKey: "",
                        secretKey: "",
                        description: "Binance Testnet API"
                    ),
                    autoSelectByMode: true
                )
            ),
            market: MarketSettings(
                tickSize: 0.01,
                minPriceDiffMultiplier: 100
            ),
            strategy: StrategySettings(
                partFunds: PartFunds(current: 40, initial: 40),
                partOrdersCount: 3,
                tpRate: 1.002,
                mergeInterval: 20,
                priceProtection: PriceProtection(
                    enable: true,
                    minPriceGapTicks: 10,
                    minProfitRate: 1.001
                ),
                priceGradient: PriceGradient(
                    enable: true,
                    stepSize: 2.0,
                    intraPartStep: 2.0,
                    interPartStep: 2.0
                )
            ),
            timing: TimingSettings(
                loopIntervalSeconds: 5
            ),
            storage: StorageSettings(
                mode: "sqlite_only",
                sqliteDbPath: "data/trading_bot.db",
                performance: PerformanceSettings(
                    enableMonitoring: true,
                    logSlowQueries: true,
                    slowQueryThresholdMs: 100
                )
            )
        )
    }
}

// MARK: - Extensions

extension DateFormatter {
    static let backupFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyyMMdd_HHmmss"
        return formatter
    }()
}
