//
//  TradingBotApp.swift
//  TradingBot
//
//  Created by jason on 20/7/2025.
//

import SwiftUI

@main
struct TradingBotApp: App {
    @StateObject private var databaseManager = DatabaseManager()

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(databaseManager)
        }
        .windowStyle(.titleBar)
        .windowToolbarStyle(.unified)
        .commands {
            CommandGroup(replacing: .newItem) {
                But<PERSON>("刷新数据") {
                    databaseManager.loadData()
                }
                .keyboardShortcut("r", modifiers: .command)

                <PERSON><PERSON>("重新连接数据库") {
                    databaseManager.refreshDatabaseConnection()
                }
                .keyboardShortcut("r", modifiers: [.command, .shift])

                Divider()

                But<PERSON>("选择数据库文件") {
                    databaseManager.selectDatabaseFile()
                }
                .keyboardShortcut("o", modifiers: .command)
            }
        }
    }
}
