#!/usr/bin/env python3
"""
核弹级修复：彻底移除所有bot_4.2和Python相关引用
"""

import os
import re
from datetime import datetime

def nuclear_fix_xcode_project():
    project_root = "/Users/<USER>/my_projects/TradingBot"
    project_file = os.path.join(project_root, "TradingBot.xcodeproj", "project.pbxproj")
    
    print(f"💥 核弹级修复Xcode项目文件: {project_file}")
    
    # 备份原文件
    backup_file = f"{project_file}.nuclear_backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    with open(project_file, 'r') as f:
        content = f.read()
    
    with open(backup_file, 'w') as f:
        f.write(content)
    print(f"💾 已备份到: {backup_file}")
    
    # 逐行处理，移除所有相关引用
    lines = content.split('\n')
    new_lines = []
    skip_block = False
    
    for i, line in enumerate(lines):
        # 检查是否需要跳过整个块
        if '96DA19632E2DF19700B2AE07 /* bot_4.2 */' in line and '{' in line:
            skip_block = True
            print(f"🗑️ 跳过bot_4.2定义块: {line.strip()}")
            continue
        
        if skip_block and '};' in line:
            skip_block = False
            print(f"🗑️ 结束跳过块: {line.strip()}")
            continue
        
        if skip_block:
            print(f"🗑️ 跳过块内容: {line.strip()}")
            continue
        
        # 移除包含bot_4.2的行
        if any(pattern in line for pattern in [
            'bot_4.2',
            'venv/lib/python',
            'site-packages',
            'numpy',
            'cryptography',
            '96DA19632E2DF19700B2AE07'
        ]):
            print(f"🗑️ 移除行: {line.strip()}")
            continue
        
        # 修复children列表（移除bot_4.2引用后的逗号问题）
        if 'children = (' in line and i + 1 < len(lines):
            # 检查接下来的几行，构建新的children列表
            children_lines = [line]
            j = i + 1
            while j < len(lines) and ');' not in lines[j]:
                if not any(pattern in lines[j] for pattern in ['bot_4.2', '96DA19632E2DF19700B2AE07']):
                    children_lines.append(lines[j])
                j += 1
            if j < len(lines):
                children_lines.append(lines[j])  # 添加结束的 );
            
            # 重新构建children块
            new_lines.extend(children_lines)
            # 跳过已处理的行
            while i < j:
                i += 1
            continue
        
        new_lines.append(line)
    
    # 写入修复后的文件
    new_content = '\n'.join(new_lines)
    with open(project_file, 'w') as f:
        f.write(new_content)
    
    # 验证结果
    bot_refs = len([line for line in new_lines if 'bot_4.2' in line])
    python_refs = len([line for line in new_lines if any(pattern in line for pattern in ['.py', 'venv', 'numpy', 'site-packages'])])
    
    print(f"📊 剩余bot_4.2引用: {bot_refs}")
    print(f"📊 剩余Python引用: {python_refs}")
    print("💥 核弹级修复完成！")
    
    # 清理缓存
    print("🗑️ 清理Xcode缓存...")
    os.system("rm -rf ~/Library/Developer/Xcode/DerivedData/TradingBot-*")
    
    print("\n📋 现在请：")
    print("1. 关闭Xcode（如果正在运行）")
    print("2. 重新打开Xcode项目")
    print("3. Product → Clean Build Folder (⇧⌘K)")
    print("4. Product → Build (⌘B)")

if __name__ == "__main__":
    nuclear_fix_xcode_project()
