#!/bin/bash

# 通用项目还原脚本
# 从备份ZIP文件还原项目，允许用户选择要还原的备份版本
# 还原前会删除当前项目中除了还原脚本以外的所有文件
#
# 用法: ./restore_project.sh
#
# 使用上下箭头键选择要还原的备份版本，按Enter确认

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # 无颜色

# 获取当前目录名称作为项目名称
PROJECT_DIR="$(pwd)"
PROJECT_NAME="$(basename "$PROJECT_DIR")"
BACKUP_DIR="$HOME/my_project_backups"
PROJECT_BACKUP_DIR="${BACKUP_DIR}/${PROJECT_NAME}"
RESTORE_SCRIPT="$(basename "$0")"
TEMP_DIR="/tmp/restore_${PROJECT_NAME}_$(date +%s)"

echo -e "${CYAN}=== $PROJECT_NAME 项目还原工具 ===${NC}"
echo -e "${BLUE}项目名称: $PROJECT_NAME${NC}"
echo -e "${BLUE}项目目录: $PROJECT_DIR${NC}"
echo -e "${BLUE}主备份目录: $BACKUP_DIR${NC}"
echo -e "${BLUE}项目备份目录: $PROJECT_BACKUP_DIR${NC}"
echo -e "${BLUE}临时目录: $TEMP_DIR${NC}"
echo ""

# 检查主备份目录是否存在
if [ ! -d "$BACKUP_DIR" ]; then
    echo -e "${RED}错误: 主备份目录不存在: $BACKUP_DIR${NC}"
    exit 1
fi

# 检查项目备份目录是否存在
if [ ! -d "$PROJECT_BACKUP_DIR" ]; then
    echo -e "${YELLOW}警告: 项目备份目录不存在: $PROJECT_BACKUP_DIR${NC}"
    echo -e "${YELLOW}将创建项目备份目录${NC}"
    mkdir -p "$PROJECT_BACKUP_DIR"
fi

# 获取所有备份文件并按时间排序（最新的在前）
BACKUP_FILES=($(ls -t "$PROJECT_BACKUP_DIR"/*.zip 2>/dev/null))

# 检查是否有备份文件
if [ ${#BACKUP_FILES[@]} -eq 0 ]; then
    echo -e "${YELLOW}警告: 在 $PROJECT_BACKUP_DIR 中未找到 ${PROJECT_NAME} 的备份文件${NC}"
    echo -e "${YELLOW}您可以先使用 backup_project.sh 创建备份${NC}"
    read -p "是否继续? (y/n): " continue_without_backup
    if [[ ! $continue_without_backup =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}操作已取消${NC}"
        exit 0
    fi
    echo -e "${RED}警告: 继续操作将清空项目目录，但没有备份可以还原!${NC}"
    read -p "确认继续? (y/n): " confirm_continue
    if [[ ! $confirm_continue =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}操作已取消${NC}"
        exit 0
    fi
    # 创建一个空的临时目录作为源
    mkdir -p "$TEMP_DIR/empty"
    EXTRACTED_DIR="$TEMP_DIR/empty"
    BACKUP_FILENAME="无备份"
else
    # 正常流程，有备份文件
    echo -e "${GREEN}找到 ${#BACKUP_FILES[@]} 个备份文件${NC}"
fi

# 显示备份选择菜单
function show_menu {
    clear
    echo -e "${CYAN}=== $PROJECT_NAME 项目还原 ===${NC}"
    echo -e "${YELLOW}请选择要还原的备份版本:${NC}"
    echo ""

    # 计算当前页面应该显示的备份范围
    local page_size=15
    local start_idx=$((PAGE * page_size))
    local end_idx=$(( (PAGE + 1) * page_size - 1 ))

    # 确保结束索引不超过备份文件总数
    if [ $end_idx -ge $TOTAL ]; then
        end_idx=$((TOTAL - 1))
    fi

    # 显示页面信息
    if [ $TOTAL -gt $page_size ]; then
        local total_pages=$(( (TOTAL + page_size - 1) / page_size ))
        echo -e "${CYAN}第 $((PAGE + 1))/$total_pages 页 (共 $TOTAL 个备份)${NC}"
        echo -e "${YELLOW}使用PageUp/PageDown翻页${NC}"
        echo ""
    fi

    # 显示当前页面的备份
    for i in $(seq $start_idx $end_idx); do
        FILENAME=$(basename "${BACKUP_FILES[$i]}")
        FILESIZE=$(du -h "${BACKUP_FILES[$i]}" | cut -f1)
        DATE_PART=$(echo "$FILENAME" | grep -o "[0-9]\{8\}_[0-9]\{6\}")
        FORMATTED_DATE=$(date -j -f "%Y%m%d_%H%M%S" "$DATE_PART" "+%Y-%m-%d %H:%M:%S" 2>/dev/null || echo "$DATE_PART")
        DESCRIPTION=$(echo "$FILENAME" | sed -E "s/${PROJECT_NAME}_[0-9]{8}_[0-9]{6}_(.*)\.zip/\1/" | tr '_' ' ')

        if [ $i -eq $SELECTED ]; then
            echo -e "${GREEN}→ $((i+1)). $FORMATTED_DATE | $FILESIZE | $DESCRIPTION${NC}"
        else
            echo -e "  $((i+1)). $FORMATTED_DATE | $FILESIZE | $DESCRIPTION"
        fi
    done

    echo ""
    echo -e "${YELLOW}使用上下箭头键选择，Fn+上下箭头翻页，按Enter确认，按Ctrl+C取消${NC}"
}

# 如果有备份文件，显示选择菜单
if [ ${#BACKUP_FILES[@]} -gt 0 ]; then
    # 初始化选择为第一个（最新的）备份
    SELECTED=0
    TOTAL=${#BACKUP_FILES[@]}
    PAGE=0  # 初始化页码为0（第一页）

    # 计算页面大小和总页数
    PAGE_SIZE=15
    TOTAL_PAGES=$(( (TOTAL + PAGE_SIZE - 1) / PAGE_SIZE ))

    # 显示菜单并处理键盘输入
    show_menu

    # 使用read命令捕获键盘输入
    while true; do
        # 使用read命令捕获单个按键
        read -rsn1 key

        # 检查是否是特殊键序列（如箭头键、PageUp/PageDown等）
        if [[ $key == $'\x1b' ]]; then
            read -rsn2 key
            if [[ $key == "[A" ]]; then  # 上箭头
                ((SELECTED--))
                if [ $SELECTED -lt 0 ]; then
                    SELECTED=$((TOTAL-1))
                fi
                # 计算SELECTED所在的页面
                PAGE=$((SELECTED / PAGE_SIZE))
                show_menu
            elif [[ $key == "[B" ]]; then  # 下箭头
                ((SELECTED++))
                if [ $SELECTED -ge $TOTAL ]; then
                    SELECTED=0
                fi
                # 计算SELECTED所在的页面
                PAGE=$((SELECTED / PAGE_SIZE))
                show_menu
            elif [[ $key == "[5~" || $key == "[1;2A" ]]; then  # PageUp 或 Fn+上箭头
                # 向上翻页，但保持选择项在相对位置
                local old_relative_pos=$((SELECTED % PAGE_SIZE))
                ((PAGE--))
                if [ $PAGE -lt 0 ]; then
                    PAGE=$((TOTAL_PAGES-1))
                fi
                # 计算新的选择项
                SELECTED=$((PAGE * PAGE_SIZE + old_relative_pos))
                # 确保不超出范围
                if [ $SELECTED -ge $TOTAL ]; then
                    SELECTED=$((TOTAL-1))
                fi
                show_menu
            elif [[ $key == "[6~" || $key == "[1;2B" ]]; then  # PageDown 或 Fn+下箭头
                # 向下翻页，但保持选择项在相对位置
                local old_relative_pos=$((SELECTED % PAGE_SIZE))
                ((PAGE++))
                if [ $PAGE -ge $TOTAL_PAGES ]; then
                    PAGE=0
                fi
                # 计算新的选择项
                SELECTED=$((PAGE * PAGE_SIZE + old_relative_pos))
                # 确保不超出范围
                if [ $SELECTED -ge $TOTAL ]; then
                    SELECTED=$((TOTAL-1))
                fi
                show_menu
            fi
        elif [[ $key == "" ]]; then  # Enter键
            break
        fi
    done

    # 获取选择的备份文件
    SELECTED_BACKUP="${BACKUP_FILES[$SELECTED]}"
    BACKUP_FILENAME=$(basename "$SELECTED_BACKUP")

    # 确认还原
    clear
    echo -e "${CYAN}=== $PROJECT_NAME 项目还原 ===${NC}"
    echo -e "${YELLOW}您选择了以下备份:${NC}"
    echo -e "文件: ${GREEN}$BACKUP_FILENAME${NC}"
    echo -e "大小: ${GREEN}$(du -h "$SELECTED_BACKUP" | cut -f1)${NC}"
    echo ""
    # 直接继续执行，不再询问确认

    # 解压备份文件到临时目录
    echo -e "${BLUE}解压备份文件...${NC}"
    unzip -q "$SELECTED_BACKUP" -d "$TEMP_DIR"

    # 获取解压后的项目目录（应该是项目名称的目录）
    EXTRACTED_DIR="$TEMP_DIR/$PROJECT_NAME"

    # 检查项目目录是否存在
    if [ ! -d "$EXTRACTED_DIR" ]; then
        # 如果没有找到项目名称目录，尝试查找任何解压出的目录
        EXTRACTED_DIR=$(find "$TEMP_DIR" -type d -depth 1)

        if [ -z "$EXTRACTED_DIR" ]; then
            echo -e "${RED}错误: 无法找到解压后的目录${NC}"
            rm -rf "$TEMP_DIR"
            exit 1
        fi

        echo -e "${YELLOW}警告: 未找到预期的项目目录结构，使用找到的第一个目录: ${CYAN}$EXTRACTED_DIR${NC}"
    fi
else
    # 没有备份文件，已经在前面设置了EXTRACTED_DIR为空目录
    clear
    echo -e "${CYAN}=== $PROJECT_NAME 项目还原 ===${NC}"
    echo -e "${YELLOW}没有可用的备份文件，将清空项目目录${NC}"
    echo ""
fi

# 临时目录已在前面创建

# 删除当前项目中除了还原脚本以外的所有文件
echo -e "${BLUE}删除当前项目文件...${NC}"
find "$PROJECT_DIR" -mindepth 1 -not -name "$RESTORE_SCRIPT" -not -path "*/\.*" | xargs rm -rf

# 添加2秒延迟，让用户能够看到删除效果
echo -e "${YELLOW}等待2秒，观察删除效果...${NC}"
sleep 2

# 显示删除后的文件列表（只显示文件数量）
echo -e "${BLUE}删除后剩余文件数量: $(find "$PROJECT_DIR" -mindepth 1 | wc -l)${NC}"

# 复制解压后的文件到项目目录
echo -e "${BLUE}还原项目文件...${NC}"

# 保存当前脚本的内容
SCRIPT_CONTENT=$(cat "$PROJECT_DIR/$RESTORE_SCRIPT")

# 复制文件，保持目录结构，但排除还原脚本
echo -e "${BLUE}正在复制文件，保持原有目录结构...${NC}"
# 使用rsync保持目录结构并排除还原脚本
rsync -a --exclude="$RESTORE_SCRIPT" "$EXTRACTED_DIR/" "$PROJECT_DIR/"

# 如果备份中包含还原脚本，则不覆盖当前的脚本
if [ -f "$EXTRACTED_DIR/$RESTORE_SCRIPT" ]; then
    echo -e "${YELLOW}备份中包含还原脚本，但不会覆盖当前脚本${NC}"
fi

# 添加2秒延迟，让用户能够看到复制效果
echo -e "${YELLOW}等待2秒，观察复制效果...${NC}"
sleep 2

# 显示复制后的文件列表（只显示文件数量）
echo -e "${BLUE}复制后项目文件数量: $(find "$PROJECT_DIR" -mindepth 1 | wc -l)${NC}"

# 清理临时目录
echo -e "${BLUE}清理临时目录...${NC}"
rm -rf "$TEMP_DIR"

# 完成
echo -e "${GREEN}还原成功完成!${NC}"
echo -e "项目已从备份 ${CYAN}$BACKUP_FILENAME${NC} 还原"
echo -e "还原时间: $(date)"

# 直接退出，不等待按键
exit 0
