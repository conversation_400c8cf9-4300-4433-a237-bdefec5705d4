//
//  LogViewerView.swift
//  TradingBot
//
//  Created by Assistant on 2025-01-21.
//

import SwiftUI
import Foundation

struct LogViewerView: View {
    @StateObject private var logManager = LogManager()
    @State private var selectedLogType: LogType = .all
    @State private var searchText = ""
    @State private var isAutoRefresh = true
    @State private var showingExportSheet = false
    
    var body: some View {
        VStack(spacing: 0) {
            // 工具栏
            LogToolbar(
                selectedLogType: $selectedLogType,
                searchText: $searchText,
                isAutoRefresh: $isAutoRefresh,
                showingExportSheet: $showingExportSheet,
                logManager: logManager
            )
            
            Divider()
            
            // 日志内容
            LogContent(
                logManager: logManager,
                selectedLogType: selectedLogType,
                searchText: searchText
            )
        }
        .navigationTitle("日志查看器")
        .onAppear {
            logManager.startMonitoring()
        }
        .onDisappear {
            logManager.stopMonitoring()
        }
        .sheet(isPresented: $showingExportSheet) {
            LogExportView(logManager: logManager)
        }
    }
}

// MARK: - 日志类型

enum LogType: String, CaseIterable {
    case all = "全部"
    case info = "信息"
    case warning = "警告"
    case error = "错误"
    case trading = "交易"
    case system = "系统"
    
    var icon: String {
        switch self {
        case .all: return "doc.text"
        case .info: return "info.circle"
        case .warning: return "exclamationmark.triangle"
        case .error: return "xmark.circle"
        case .trading: return "chart.line.uptrend.xyaxis"
        case .system: return "gear"
        }
    }
    
    var color: Color {
        switch self {
        case .all: return .primary
        case .info: return .blue
        case .warning: return .orange
        case .error: return .red
        case .trading: return .green
        case .system: return .purple
        }
    }
}

// MARK: - 日志条目模型

struct ViewerLogEntry: Identifiable {
    let id = UUID()
    let timestamp: Date
    let level: LogType
    let message: String
    let source: String

    var formattedTimestamp: String {
        DateFormatter.logTimestamp.string(from: timestamp)
    }
}

// MARK: - 日志管理器

class LogManager: ObservableObject {
    @Published var logs: [ViewerLogEntry] = []
    @Published var isMonitoring = false
    
    private var timer: Timer?
    private let logFilePaths: [String]

    init() {
        let botDirectory = Self.getBotDirectory()
        self.logFilePaths = [
            "\(botDirectory)/log/trading_bot.log",
            "\(botDirectory)/log/error.log",
            "\(botDirectory)/log/order.log"
        ]
    }

    private static func getBotDirectory() -> String {
        // 首先尝试相对于应用bundle的路径
        let bundlePath = Bundle.main.bundlePath
        let relativePath = URL(fileURLWithPath: bundlePath)
            .deletingLastPathComponent()
            .deletingLastPathComponent()
            .deletingLastPathComponent()
            .appendingPathComponent("bot_4.2")
            .path

        if FileManager.default.fileExists(atPath: relativePath) {
            return relativePath
        }

        // 备用路径
        let fallbackPaths = [
            "/Users/<USER>/my_projects/TradingBot/bot_4.2",
            "/Users/<USER>/bot/bot_4.2",
            "./bot_4.2"
        ]

        for path in fallbackPaths {
            if FileManager.default.fileExists(atPath: path) {
                return path
            }
        }

        // 默认路径
        return "/Users/<USER>/my_projects/TradingBot/bot_4.2"
    }
    
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        loadInitialLogs()
        
        // 每2秒检查一次新日志
        timer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { [weak self] _ in
            self?.checkForNewLogs()
        }
    }
    
    func stopMonitoring() {
        isMonitoring = false
        timer?.invalidate()
        timer = nil
    }
    
    func loadInitialLogs() {
        var allLogs: [ViewerLogEntry] = []
        
        for logPath in logFilePaths {
            if let newLogs = readLogFile(at: logPath) {
                allLogs.append(contentsOf: newLogs)
            }
        }
        
        // 按时间排序并限制数量
        allLogs.sort { $0.timestamp > $1.timestamp }
        
        DispatchQueue.main.async {
            self.logs = Array(allLogs.prefix(1000))
        }
    }
    
    private func checkForNewLogs() {
        // 简化实现：重新读取最新的日志
        loadInitialLogs()
    }
    
    private func readLogFile(at path: String) -> [ViewerLogEntry]? {
        guard FileManager.default.fileExists(atPath: path) else { return nil }
        
        do {
            let content = try String(contentsOfFile: path, encoding: .utf8)
            let lines = content.components(separatedBy: .newlines)
            
            return lines.compactMap { line in
                parseLogLine(line, source: URL(fileURLWithPath: path).lastPathComponent)
            }
        } catch {
            print("读取日志文件失败: \(error)")
            return nil
        }
    }
    
    private func parseLogLine(_ line: String, source: String) -> ViewerLogEntry? {
        guard !line.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return nil }
        
        // 简化的日志解析
        let timestamp = Date()
        let level = detectLogLevel(from: line)
        
        return ViewerLogEntry(
            timestamp: timestamp,
            level: level,
            message: line,
            source: source
        )
    }
    
    private func detectLogLevel(from line: String) -> LogType {
        let lowercased = line.lowercased()
        
        if lowercased.contains("error") || lowercased.contains("❌") {
            return .error
        } else if lowercased.contains("warning") || lowercased.contains("⚠️") {
            return .warning
        } else if lowercased.contains("order") || lowercased.contains("trade") {
            return .trading
        } else if lowercased.contains("system") || lowercased.contains("cpu") || lowercased.contains("memory") {
            return .system
        } else {
            return .info
        }
    }
    
    func exportLogs() -> String {
        return logs.map { log in
            "[\(log.formattedTimestamp)] [\(log.level.rawValue)] [\(log.source)] \(log.message)"
        }.joined(separator: "\n")
    }
    
    func clearLogs() {
        logs.removeAll()
    }
}

// MARK: - 工具栏

struct LogToolbar: View {
    @Binding var selectedLogType: LogType
    @Binding var searchText: String
    @Binding var isAutoRefresh: Bool
    @Binding var showingExportSheet: Bool
    @ObservedObject var logManager: LogManager
    
    var body: some View {
        HStack(spacing: 12) {
            // 日志类型选择器
            Picker("日志类型", selection: $selectedLogType) {
                ForEach(LogType.allCases, id: \.self) { type in
                    Label(type.rawValue, systemImage: type.icon)
                        .tag(type)
                }
            }
            .pickerStyle(.menu)
            .frame(width: 120)
            
            // 搜索框
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                TextField("搜索日志...", text: $searchText)
                    .textFieldStyle(.plain)
                
                if !searchText.isEmpty {
                    Button(action: { searchText = "" }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                    .buttonStyle(.plain)
                }
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color(.controlBackgroundColor))
            .cornerRadius(6)
            
            Spacer()
            
            // 自动刷新开关
            Toggle("自动刷新", isOn: $isAutoRefresh)
                .toggleStyle(.switch)
                .scaleEffect(0.8)
            
            // 操作按钮
            HStack(spacing: 8) {
                Button("刷新") {
                    logManager.loadInitialLogs()
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
                
                Button("清空") {
                    logManager.clearLogs()
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
                
                Button("导出") {
                    showingExportSheet = true
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
            }
        }
        .padding()
    }
}

// MARK: - 日志内容

struct LogContent: View {
    @ObservedObject var logManager: LogManager
    let selectedLogType: LogType
    let searchText: String
    
    var filteredLogs: [ViewerLogEntry] {
        var filtered = logManager.logs
        
        // 按类型过滤
        if selectedLogType != .all {
            filtered = filtered.filter { $0.level == selectedLogType }
        }
        
        // 按搜索文本过滤
        if !searchText.isEmpty {
            let searchLower = searchText.lowercased()
            filtered = filtered.filter { log in
                log.message.lowercased().contains(searchLower)
            }
        }
        
        return filtered
    }
    
    var body: some View {
        ScrollView {
            LazyVStack(alignment: .leading, spacing: 1) {
                if filteredLogs.isEmpty {
                    VStack(spacing: 12) {
                        Image(systemName: "doc.text")
                            .font(.largeTitle)
                            .foregroundColor(.secondary)
                        Text("暂无日志")
                            .font(.headline)
                            .foregroundColor(.secondary)
                        Text("请检查Bot是否正在运行或调整过滤条件")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .padding(.top, 100)
                } else {
                    ForEach(filteredLogs) { log in
                        LogRow(log: log)
                        Divider()
                    }
                }
            }
        }
        .background(Color(.textBackgroundColor))
    }
}

// MARK: - 日志行

struct LogRow: View {
    let log: ViewerLogEntry
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // 时间戳
            Text(log.formattedTimestamp)
                .font(.system(.caption, design: .monospaced))
                .foregroundColor(.secondary)
                .frame(width: 80, alignment: .leading)
            
            // 级别图标
            Image(systemName: log.level.icon)
                .foregroundColor(log.level.color)
                .frame(width: 16)
            
            // 消息内容
            Text(log.message)
                .font(.system(.caption, design: .monospaced))
                .textSelection(.enabled)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // 来源
            Text(log.source)
                .font(.system(.caption2, design: .monospaced))
                .foregroundColor(.secondary)
                .frame(width: 100, alignment: .trailing)
        }
        .padding(.horizontal)
        .padding(.vertical, 4)
    }
}

// MARK: - 导出视图

struct LogExportView: View {
    @ObservedObject var logManager: LogManager
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("导出日志")
                    .font(.headline)
                    .padding()
                
                Text("将导出 \(logManager.logs.count) 条日志记录")
                    .foregroundColor(.secondary)
                
                Spacer()
                
                HStack {
                    Button("取消") {
                        dismiss()
                    }
                    .buttonStyle(.bordered)
                    
                    Button("导出到文件") {
                        exportToFile()
                    }
                    .buttonStyle(.borderedProminent)
                }
                .padding()
            }
        }
        .frame(width: 400, height: 200)
    }
    
    private func exportToFile() {
        let savePanel = NSSavePanel()
        savePanel.allowedContentTypes = [.plainText]
        savePanel.nameFieldStringValue = "bot_logs_\(DateFormatter.filename.string(from: Date())).txt"
        
        if savePanel.runModal() == .OK {
            guard let url = savePanel.url else { return }
            
            do {
                try logManager.exportLogs().write(to: url, atomically: true, encoding: .utf8)
                dismiss()
            } catch {
                print("导出失败: \(error)")
            }
        }
    }
}

// MARK: - Extensions

extension DateFormatter {
    static let logTimestamp: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"
        return formatter
    }()
    
    static let filename: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyyMMdd_HHmmss"
        return formatter
    }()
}

#Preview {
    LogViewerView()
}
