//
//  FundingManager.swift
//  TradingBot
//
//  Created by Assistant on 2025-01-21.
//

import Foundation
import SwiftUI
import SQLite3

// MARK: - 数据模型
struct CryptoAccount: Identifiable {
    let id = UUID()
    let symbol: String
    let name: String
    let balance: Double
    let usdtValue: Double
    let iconURL: String

    var formattedBalance: String {
        if symbol == "BTC" || symbol == "ETH" {
            return String(format: "%.8f", balance)
        } else {
            let formatter = NumberFormatter()
            formatter.numberStyle = .decimal
            formatter.minimumFractionDigits = 2
            formatter.maximumFractionDigits = 2
            return formatter.string(from: NSNumber(value: balance)) ?? "0.00"
        }
    }

    var formattedUSDTValue: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        formatter.minimumFractionDigits = 2
        formatter.maximumFractionDigits = 2
        return formatter.string(from: NSNumber(value: usdtValue)) ?? "0.00"
    }
}

struct AccountOverview {
    let totalValue: Double
}

// MARK: - 资金管理器
@MainActor
class FundingManager: ObservableObject {
    @Published var cryptoAccounts: [CryptoAccount] = []
    @Published var totalValue: Double = 0.0
    @Published var isLoading: Bool = false
    @Published var lastUpdateTime: Date = Date()
    @Published var errorMessage: String?

    private let databaseManager: DatabaseManager

    // 自动刷新定时器
    private var refreshTimer: Timer?
    private let autoRefreshInterval: TimeInterval = 30 // 30秒自动刷新

    init(databaseManager: DatabaseManager) {
        self.databaseManager = databaseManager
        loadAccountData()
        startAutoRefresh()
    }

    deinit {
        refreshTimer?.invalidate()
    }

    // MARK: - 公共方法
    func loadAccountData() {
        Task {
            await refreshAccountData()
        }
    }

    func refreshAccountData() async {
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }

        // 从SQLite数据库获取assets数据
        let assetsData = await getAssetsFromDatabase()

        // 从market_monitor表获取价格数据
        let pricesData = await getPricesFromDatabase()

        // 计算账户价值
        let accounts = calculateAccountValues(assetsData: assetsData, pricesData: pricesData)

        await updateUI(with: accounts)
    }

    private func updateUI(with accounts: [CryptoAccount]) async {
        await MainActor.run {
            self.cryptoAccounts = accounts
            self.totalValue = accounts.reduce(0) { $0 + $1.usdtValue }
            self.lastUpdateTime = Date()
            self.isLoading = false
        }
    }
    
    // MARK: - 私有方法

    // 从SQLite数据库获取assets数据
    private func getAssetsFromDatabase() async -> [String: Double] {
        return await withCheckedContinuation { continuation in
            databaseManager.getAssetsData { assetsData in
                // 如果没有数据，提供一些模拟数据用于测试
                if assetsData.isEmpty {
                    print("⚠️ 未获取到assets数据，使用模拟数据")
                    let mockData: [String: Double] = [
                        "USDT": 80.0,
                        "BTC": 0.001,
                        "ETH": 0.02
                    ]
                    continuation.resume(returning: mockData)
                } else {
                    continuation.resume(returning: assetsData)
                }
            }
        }
    }

    // 从SQLite数据库获取market_monitor价格数据
    private func getPricesFromDatabase() async -> [String: Double] {
        return await withCheckedContinuation { continuation in
            databaseManager.getMarketPrices { pricesData in
                // 如果没有数据，提供一些模拟价格数据
                if pricesData.isEmpty {
                    print("⚠️ 未获取到价格数据，使用模拟数据")
                    let mockPrices: [String: Double] = [
                        "BTC/USDT": 100000.0,
                        "ETH/USDT": 3500.0
                    ]
                    continuation.resume(returning: mockPrices)
                } else {
                    continuation.resume(returning: pricesData)
                }
            }
        }
    }

    // 计算账户价值（简化版本）
    private func calculateAccountValues(assetsData: [String: Double], pricesData: [String: Double]) -> [CryptoAccount] {
        var accounts: [CryptoAccount] = []

        for (symbol, balance) in assetsData {
            guard balance > 0 else { continue }

            let usdtValue: Double

            if symbol == "USDT" {
                usdtValue = balance
            } else {
                // 从market_monitor获取价格，symbol格式为"BTC/USDT"
                let priceSymbol = "\(symbol)/USDT"
                let price = pricesData[priceSymbol] ?? 0.0
                usdtValue = balance * price
            }

            let account = CryptoAccount(
                symbol: symbol,
                name: getCryptoName(symbol: symbol),
                balance: balance,
                usdtValue: usdtValue,
                iconURL: getCryptoIconURL(symbol: symbol)
            )

            accounts.append(account)
        }

        return accounts.sorted { $0.usdtValue > $1.usdtValue }
    }
    
    private func getCryptoName(symbol: String) -> String {
        let cryptoNames: [String: String] = [
            "BTC": "Bitcoin",
            "ETH": "Ethereum",
            "BNB": "BNB",
            "USDT": "Tether",
            "USDC": "USD Coin",
            "PEPE": "Pepe",
            "FDUSD": "First Digital USD",
            "SOL": "Solana",
            "ADA": "Cardano",
            "DOT": "Polkadot"
        ]
        return cryptoNames[symbol] ?? symbol
    }
    
    private func getCryptoIconURL(symbol: String) -> String {
        return "https://cryptoicons.org/api/icon/\(symbol.lowercased())/200"
    }
    
    // MARK: - 格式化方法
    func formatCurrency(_ value: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        formatter.minimumFractionDigits = 2
        formatter.maximumFractionDigits = 2
        return formatter.string(from: NSNumber(value: value)) ?? "0.00"
    }
    
    func formatPercentage(_ value: Double) -> String {
        let sign = value >= 0 ? "+" : ""
        return "\(sign)\(String(format: "%.2f", value))%"
    }
    
    // MARK: - 自动刷新管理

    private func startAutoRefresh() {
        refreshTimer = Timer.scheduledTimer(withTimeInterval: autoRefreshInterval, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.refreshAccountData()
            }
        }
    }

    private func stopAutoRefresh() {
        refreshTimer?.invalidate()
        refreshTimer = nil
    }

    func pauseAutoRefresh() {
        stopAutoRefresh()
    }

    func resumeAutoRefresh() {
        startAutoRefresh()
    }

    // MARK: - 数据导出

    func exportAccountData() -> [String: Any] {
        return [
            "totalValue": totalValue,
            "lastUpdateTime": lastUpdateTime.timeIntervalSince1970,
            "accounts": cryptoAccounts.map { account in
                [
                    "symbol": account.symbol,
                    "name": account.name,
                    "balance": account.balance,
                    "usdtValue": account.usdtValue
                ]
            }
        ]
    }

    // MARK: - 统计信息

    func getTotalPortfolioCount() -> Int {
        return cryptoAccounts.count
    }
}
