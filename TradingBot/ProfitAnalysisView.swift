//
//  ProfitAnalysisView.swift
//  TradingBot
//
//  Created by Assistant on 2025-01-20.
//

import SwiftUI

struct ProfitAnalysisView: View {
    @ObservedObject var databaseManager: DatabaseManager
    @State private var selectedTimeRange = TimeRange.today
    @State private var partFunds: [PartFund] = []
    
    enum TimeRange: String, CaseIterable {
        case today = "今日"
        case week = "本周"
        case month = "本月"
        case all = "全部"
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 时间范围选择器
                TimeRangeSelector(selectedRange: $selectedTimeRange)
                
                // 收益概览卡片
                ProfitOverviewCards(databaseManager: databaseManager)
                
                // Part资金详情表格
                PartFundsTable(partFunds: partFunds)
                
                // 收益趋势图表（占位）
                ProfitTrendChart()
            }
            .padding()
        }
        .navigationTitle("收益分析")
        .onAppear {
            loadPartFunds()
        }
        .onChange(of: databaseManager.lastUpdate) { _, _ in
            loadPartFunds()
        }
    }
    
    private func loadPartFunds() {
        // 这里应该从数据库加载详细的part资金数据
        // 暂时使用模拟数据
        partFunds = [
            PartFund(name: "part_1", currentFunds: 40.0, initialFunds: 40.0, totalProfit: 0.0, tradeCount: 0),
            PartFund(name: "part_2", currentFunds: 40.0, initialFunds: 40.0, totalProfit: 0.0, tradeCount: 0),
            PartFund(name: "part_3", currentFunds: 40.0, initialFunds: 40.0, totalProfit: 0.0, tradeCount: 0),
            PartFund(name: "part_4", currentFunds: 40.0, initialFunds: 40.0, totalProfit: 0.0, tradeCount: 0)
        ]
    }
}

struct TimeRangeSelector: View {
    @Binding var selectedRange: ProfitAnalysisView.TimeRange
    
    var body: some View {
        HStack {
            Text("时间范围:")
                .font(.headline)
            
            Spacer()
            
            Picker("时间范围", selection: $selectedRange) {
                ForEach(ProfitAnalysisView.TimeRange.allCases, id: \.self) { range in
                    Text(range.rawValue).tag(range)
                }
            }
            .pickerStyle(.segmented)
            .frame(maxWidth: 300)
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }
}

struct ProfitOverviewCards: View {
    @ObservedObject var databaseManager: DatabaseManager
    
    var body: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 16) {
            OverviewCard(
                title: "总收益率",
                value: databaseManager.formatPercentage(databaseManager.profitRate),
                color: databaseManager.totalProfit >= 0 ? .green : .red,
                icon: "percent"
            )
            
            OverviewCard(
                title: "平均每笔",
                value: databaseManager.averageProfitPerTrade,
                color: .blue,
                icon: "chart.bar.fill"
            )
            
            OverviewCard(
                title: "胜率",
                value: "95.2%", // 示例数据
                color: .orange,
                icon: "target"
            )
        }
    }
}

struct OverviewCard: View {
    let title: String
    let value: String
    let color: Color
    let icon: String
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .frame(maxWidth: .infinity)
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }
}

struct PartFundsTable: View {
    let partFunds: [PartFund]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Part资金详情")
                .font(.headline)
            
            if partFunds.isEmpty {
                Text("暂无数据")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, minHeight: 100)
                    .background(Color(.controlBackgroundColor))
                    .cornerRadius(12)
            } else {
                LazyVStack(spacing: 8) {
                    // 表头
                    HStack {
                        Text("Part")
                            .font(.caption)
                            .fontWeight(.medium)
                            .frame(maxWidth: .infinity, alignment: .leading)
                        
                        Text("当前资金")
                            .font(.caption)
                            .fontWeight(.medium)
                            .frame(maxWidth: .infinity, alignment: .trailing)
                        
                        Text("收益")
                            .font(.caption)
                            .fontWeight(.medium)
                            .frame(maxWidth: .infinity, alignment: .trailing)
                        
                        Text("收益率")
                            .font(.caption)
                            .fontWeight(.medium)
                            .frame(maxWidth: .infinity, alignment: .trailing)
                        
                        Text("交易次数")
                            .font(.caption)
                            .fontWeight(.medium)
                            .frame(maxWidth: .infinity, alignment: .trailing)
                    }
                    .padding(.horizontal)
                    .padding(.vertical, 8)
                    .background(Color(.secondarySystemFill))
                    .cornerRadius(8)
                    
                    // 数据行
                    ForEach(partFunds, id: \.name) { part in
                        PartFundRow(part: part)
                    }
                }
                .background(Color(.controlBackgroundColor))
                .cornerRadius(12)
            }
        }
    }
}

struct PartFundRow: View {
    let part: PartFund
    
    var body: some View {
        HStack {
            Text(part.name)
                .font(.body)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            Text("$\(part.currentFunds, specifier: "%.2f")")
                .font(.body)
                .frame(maxWidth: .infinity, alignment: .trailing)
            
            Text("$\(part.totalProfit, specifier: "%.2f")")
                .font(.body)
                .foregroundColor(part.totalProfit >= 0 ? .green : .red)
                .frame(maxWidth: .infinity, alignment: .trailing)
            
            Text("\(part.profitRate, specifier: "%.2f")%")
                .font(.body)
                .foregroundColor(part.totalProfit >= 0 ? .green : .red)
                .frame(maxWidth: .infinity, alignment: .trailing)
            
            Text("\(part.tradeCount)")
                .font(.body)
                .frame(maxWidth: .infinity, alignment: .trailing)
        }
        .padding(.horizontal)
        .padding(.vertical, 12)
        .background(Color(.controlBackgroundColor))
    }
}

struct ProfitTrendChart: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("收益趋势")
                .font(.headline)
            
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.controlBackgroundColor))
                .frame(height: 250)
                .overlay(
                    VStack {
                        Image(systemName: "chart.line.uptrend.xyaxis")
                            .font(.largeTitle)
                            .foregroundColor(.secondary)
                        Text("收益趋势图表")
                            .font(.headline)
                            .foregroundColor(.secondary)
                        Text("开发中...")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                )
        }
    }
}

struct PartFund {
    let name: String
    let currentFunds: Double
    let initialFunds: Double
    let totalProfit: Double
    let tradeCount: Int
    
    var profitRate: Double {
        guard initialFunds > 0 else { return 0 }
        return (totalProfit / initialFunds) * 100
    }
}

#Preview {
    ProfitAnalysisView(databaseManager: DatabaseManager())
}
