//
//  SecurityConfirmationView.swift
//  TradingBot
//
//  Created by Assistant on 2025-01-21.
//

import SwiftUI

// MARK: - 安全确认对话框

struct SecurityConfirmationDialog: View {
    let action: SecurityAction
    let warnings: [String]
    let onConfirm: () -> Void
    let onCancel: () -> Void
    
    @State private var userConfirmed = false
    
    var body: some View {
        VStack(spacing: 20) {
            // 图标和标题
            VStack(spacing: 12) {
                Image(systemName: action.isDestructive ? "exclamationmark.triangle.fill" : "questionmark.circle.fill")
                    .font(.largeTitle)
                    .foregroundColor(action.isDestructive ? .orange : .blue)
                
                Text(action.title)
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            // 操作描述
            Text(action.message)
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
            
            // 警告信息
            if !warnings.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.orange)
                        Text("注意事项:")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    
                    ForEach(warnings, id: \.self) { warning in
                        HStack(alignment: .top, spacing: 8) {
                            Text("•")
                                .foregroundColor(.orange)
                            Text(warning)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding()
                .background(Color.orange.opacity(0.1))
                .cornerRadius(8)
            }
            
            // 确认复选框（对于危险操作）
            if action.isDestructive {
                Toggle("我了解此操作的风险", isOn: $userConfirmed)
                    .toggleStyle(.checkbox)
            }
            
            // 按钮
            HStack(spacing: 12) {
                Button("取消") {
                    onCancel()
                }
                .buttonStyle(.bordered)
                .keyboardShortcut(.escape)
                
                Button(action.isDestructive ? "确认执行" : "确认") {
                    onConfirm()
                }
                .buttonStyle(.borderedProminent)
                .disabled(action.isDestructive && !userConfirmed)
                .keyboardShortcut(.return)
            }
        }
        .padding(24)
        .frame(width: 400)
    }
}

// MARK: - 备份管理视图

struct BackupManagementView: View {
    @StateObject private var securityManager = SecurityManager()
    @State private var showingCreateBackupDialog = false
    @State private var newBackupDescription = ""
    @State private var selectedBackup: BackupInfo?
    @State private var showingRestoreConfirmation = false
    @State private var showingDeleteConfirmation = false
    
    var body: some View {
        VStack(spacing: 20) {
            // 标题和创建按钮
            HStack {
                Text("备份管理")
                    .font(.headline)
                
                Spacer()
                
                Button("创建备份") {
                    showingCreateBackupDialog = true
                }
                .buttonStyle(.borderedProminent)
            }
            
            // 备份列表
            if securityManager.availableBackups.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "archivebox")
                        .font(.largeTitle)
                        .foregroundColor(.secondary)
                    Text("暂无备份")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    Text("创建第一个备份来保护您的设置")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                List {
                    ForEach(securityManager.availableBackups) { backup in
                        BackupRow(
                            backup: backup,
                            onRestore: {
                                selectedBackup = backup
                                showingRestoreConfirmation = true
                            },
                            onDelete: {
                                selectedBackup = backup
                                showingDeleteConfirmation = true
                            }
                        )
                    }
                }
            }
            
            // 错误信息
            if let error = securityManager.lastError {
                Text(error)
                    .font(.caption)
                    .foregroundColor(.red)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(4)
            }
        }
        .padding()
        .onAppear {
            securityManager.loadAvailableBackups()
        }
        .sheet(isPresented: $showingCreateBackupDialog) {
            CreateBackupDialog(
                description: $newBackupDescription,
                onCreate: {
                    securityManager.createBackup(description: newBackupDescription.isEmpty ? "手动备份" : newBackupDescription)
                    newBackupDescription = ""
                    showingCreateBackupDialog = false
                },
                onCancel: {
                    newBackupDescription = ""
                    showingCreateBackupDialog = false
                }
            )
        }
        .alert("恢复备份", isPresented: $showingRestoreConfirmation) {
            Button("取消", role: .cancel) { }
            Button("恢复", role: .destructive) {
                if let backup = selectedBackup {
                    securityManager.restoreBackup(backup)
                }
            }
        } message: {
            Text("确定要恢复此备份吗？当前设置将被覆盖。")
        }
        .alert("删除备份", isPresented: $showingDeleteConfirmation) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                if let backup = selectedBackup {
                    securityManager.deleteBackup(backup)
                }
            }
        } message: {
            Text("确定要删除此备份吗？删除后无法恢复。")
        }
    }
}

// MARK: - 备份行

struct BackupRow: View {
    let backup: BackupInfo
    let onRestore: () -> Void
    let onDelete: () -> Void
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(backup.description)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(backup.formattedDate)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(backup.formattedSize)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            HStack(spacing: 8) {
                Button("恢复") {
                    onRestore()
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
                
                Button("删除") {
                    onDelete()
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
                .foregroundColor(.red)
            }
        }
        .padding(.vertical, 4)
    }
}

// MARK: - 创建备份对话框

struct CreateBackupDialog: View {
    @Binding var description: String
    let onCreate: () -> Void
    let onCancel: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            Text("创建备份")
                .font(.headline)
            
            VStack(alignment: .leading, spacing: 8) {
                Text("备份描述 (可选)")
                    .font(.subheadline)
                
                TextField("输入备份描述...", text: $description)
                    .textFieldStyle(.roundedBorder)
            }
            
            HStack {
                Button("取消") {
                    onCancel()
                }
                .buttonStyle(.bordered)
                
                Button("创建") {
                    onCreate()
                }
                .buttonStyle(.borderedProminent)
            }
        }
        .padding()
        .frame(width: 300)
    }
}

// MARK: - 安全设置视图

struct SecuritySettingsView: View {
    @StateObject private var securityManager = SecurityManager()
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 安全概览
                SecurityOverviewCard()
                
                // 备份管理
                BackupManagementView()
                
                // 安全选项
                SecurityOptionsCard()
            }
            .padding()
        }
        .navigationTitle("安全设置")
    }
}

// MARK: - 安全概览卡片

struct SecurityOverviewCard: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("安全概览")
                .font(.headline)
            
            VStack(spacing: 8) {
                SecurityStatusItem(
                    title: "操作确认",
                    status: "已启用",
                    color: .green,
                    icon: "checkmark.shield"
                )
                
                SecurityStatusItem(
                    title: "自动备份",
                    status: "已启用",
                    color: .green,
                    icon: "archivebox"
                )
                
                SecurityStatusItem(
                    title: "进程监控",
                    status: "已启用",
                    color: .green,
                    icon: "eye"
                )
            }
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }
}

// MARK: - 安全状态项

struct SecurityStatusItem: View {
    let title: String
    let status: String
    let color: Color
    let icon: String
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(color)
                .frame(width: 20)
            
            Text(title)
                .font(.subheadline)
            
            Spacer()
            
            Text(status)
                .font(.caption)
                .foregroundColor(color)
                .padding(.horizontal, 8)
                .padding(.vertical, 2)
                .background(color.opacity(0.1))
                .cornerRadius(4)
        }
    }
}

// MARK: - 安全选项卡片

struct SecurityOptionsCard: View {
    @State private var requireConfirmation = true
    @State private var autoBackup = true
    @State private var logSensitiveOperations = true
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("安全选项")
                .font(.headline)
            
            VStack(spacing: 12) {
                Toggle("操作前需要确认", isOn: $requireConfirmation)
                    .help("危险操作前显示确认对话框")
                
                Toggle("自动创建备份", isOn: $autoBackup)
                    .help("保存设置前自动创建备份")
                
                Toggle("记录敏感操作", isOn: $logSensitiveOperations)
                    .help("在日志中记录重要的安全操作")
            }
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }
}

#Preview {
    SecuritySettingsView()
}
