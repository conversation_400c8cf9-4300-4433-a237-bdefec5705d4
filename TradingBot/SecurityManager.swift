//
//  SecurityManager.swift
//  TradingBot
//
//  Created by Assistant on 2025-01-21.
//

import Foundation
import SwiftUI

// MARK: - 安全操作类型

enum SecurityAction {
    case startBot
    case stopBot
    case restartBot
    case saveSettings
    case resetSettings
    case deleteBackup
    case restoreBackup
    
    var title: String {
        switch self {
        case .startBot: return "启动Bot"
        case .stopBot: return "停止Bot"
        case .restartBot: return "重启Bot"
        case .saveSettings: return "保存设置"
        case .resetSettings: return "重置设置"
        case .deleteBackup: return "删除备份"
        case .restoreBackup: return "恢复备份"
        }
    }
    
    var message: String {
        switch self {
        case .startBot:
            return "确定要启动Bot吗？Bot将开始执行交易策略。"
        case .stopBot:
            return "确定要停止Bot吗？这将终止所有正在进行的交易策略。"
        case .restartBot:
            return "确定要重启Bot吗？这将停止当前进程并重新启动。"
        case .saveSettings:
            return "确定要保存设置吗？新设置将在下次启动时生效。"
        case .resetSettings:
            return "确定要重置所有设置为默认值吗？此操作不可撤销。"
        case .deleteBackup:
            return "确定要删除此备份吗？删除后无法恢复。"
        case .restoreBackup:
            return "确定要恢复此备份吗？当前设置将被覆盖。"
        }
    }
    
    var isDestructive: Bool {
        switch self {
        case .stopBot, .restartBot, .resetSettings, .deleteBackup, .restoreBackup:
            return true
        case .startBot, .saveSettings:
            return false
        }
    }
    
    var requiresConfirmation: Bool {
        return true // 所有操作都需要确认
    }
}

// MARK: - 备份信息

struct BackupInfo: Identifiable, Codable {
    let id: UUID
    let filename: String
    let createdAt: Date
    let size: Int64
    let description: String

    init(filename: String, createdAt: Date, size: Int64, description: String) {
        self.id = UUID()
        self.filename = filename
        self.createdAt = createdAt
        self.size = size
        self.description = description
    }
    
    var formattedDate: String {
        DateFormatter.backup.string(from: createdAt)
    }
    
    var formattedSize: String {
        ByteCountFormatter.shared.string(fromByteCount: size)
    }
}

// MARK: - 安全管理器

class SecurityManager: ObservableObject {
    @Published var availableBackups: [BackupInfo] = []
    @Published var isLoading = false
    @Published var lastError: String?
    
    private let backupDirectory: String
    private let settingsPath: String
    
    init() {
        // 动态获取路径
        let botDirectory = Self.getBotDirectory()
        self.backupDirectory = "\(botDirectory)/backup"
        self.settingsPath = "\(botDirectory)/settings.json"

        loadAvailableBackups()
    }

    private static func getBotDirectory() -> String {
        // 首先尝试相对于应用bundle的路径
        let bundlePath = Bundle.main.bundlePath
        let relativePath = URL(fileURLWithPath: bundlePath)
            .deletingLastPathComponent()
            .deletingLastPathComponent()
            .deletingLastPathComponent()
            .appendingPathComponent("bot_4.2")
            .path

        if FileManager.default.fileExists(atPath: relativePath) {
            return relativePath
        }

        // 备用路径
        let fallbackPaths = [
            "/Users/<USER>/my_projects/TradingBot/bot_4.2",
            "/Users/<USER>/bot/bot_4.2",
            "./bot_4.2"
        ]

        for path in fallbackPaths {
            if FileManager.default.fileExists(atPath: path) {
                return path
            }
        }

        // 默认路径
        return "/Users/<USER>/my_projects/TradingBot/bot_4.2"
    }
    
    // MARK: - 备份管理
    
    func createBackup(description: String = "手动备份") {
        isLoading = true
        lastError = nil
        
        DispatchQueue.global(qos: .userInitiated).async {
            do {
                // 确保备份目录存在
                try FileManager.default.createDirectory(
                    atPath: self.backupDirectory,
                    withIntermediateDirectories: true
                )
                
                // 生成备份文件名
                let timestamp = DateFormatter.backupFilename.string(from: Date())
                let backupFilename = "settings_backup_\(timestamp).json"
                let backupPath = "\(self.backupDirectory)/\(backupFilename)"
                
                // 复制设置文件
                try FileManager.default.copyItem(atPath: self.settingsPath, toPath: backupPath)
                
                // 获取文件大小
                let attributes = try FileManager.default.attributesOfItem(atPath: backupPath)
                let fileSize = attributes[.size] as? Int64 ?? 0
                
                // 创建备份信息
                let backupInfo = BackupInfo(
                    filename: backupFilename,
                    createdAt: Date(),
                    size: fileSize,
                    description: description
                )
                
                // 保存备份元数据
                try self.saveBackupMetadata(backupInfo)
                
                DispatchQueue.main.async {
                    self.isLoading = false
                    self.loadAvailableBackups()
                }
            } catch {
                DispatchQueue.main.async {
                    self.lastError = "创建备份失败: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }
    
    func restoreBackup(_ backup: BackupInfo) {
        isLoading = true
        lastError = nil
        
        DispatchQueue.global(qos: .userInitiated).async {
            do {
                let backupPath = "\(self.backupDirectory)/\(backup.filename)"
                
                // 验证备份文件存在
                guard FileManager.default.fileExists(atPath: backupPath) else {
                    throw SecurityError.backupNotFound
                }
                
                // 创建当前设置的备份
                self.createBackup(description: "恢复前自动备份")
                
                // 恢复备份
                try FileManager.default.removeItem(atPath: self.settingsPath)
                try FileManager.default.copyItem(atPath: backupPath, toPath: self.settingsPath)
                
                DispatchQueue.main.async {
                    self.isLoading = false
                }
            } catch {
                DispatchQueue.main.async {
                    self.lastError = "恢复备份失败: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }
    
    func deleteBackup(_ backup: BackupInfo) {
        isLoading = true
        lastError = nil
        
        DispatchQueue.global(qos: .userInitiated).async {
            do {
                let backupPath = "\(self.backupDirectory)/\(backup.filename)"
                try FileManager.default.removeItem(atPath: backupPath)
                
                // 删除元数据
                try self.deleteBackupMetadata(backup)
                
                DispatchQueue.main.async {
                    self.isLoading = false
                    self.loadAvailableBackups()
                }
            } catch {
                DispatchQueue.main.async {
                    self.lastError = "删除备份失败: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }
    
    func loadAvailableBackups() {
        DispatchQueue.global(qos: .userInitiated).async {
            do {
                guard FileManager.default.fileExists(atPath: self.backupDirectory) else {
                    DispatchQueue.main.async {
                        self.availableBackups = []
                    }
                    return
                }
                
                let files = try FileManager.default.contentsOfDirectory(atPath: self.backupDirectory)
                let backupFiles = files.filter { $0.hasSuffix(".json") && $0.contains("settings_backup") }
                
                var backups: [BackupInfo] = []
                
                for filename in backupFiles {
                    let filePath = "\(self.backupDirectory)/\(filename)"
                    let attributes = try FileManager.default.attributesOfItem(atPath: filePath)
                    
                    if let creationDate = attributes[.creationDate] as? Date,
                       let fileSize = attributes[.size] as? Int64 {
                        
                        let backup = BackupInfo(
                            filename: filename,
                            createdAt: creationDate,
                            size: fileSize,
                            description: "自动备份"
                        )
                        backups.append(backup)
                    }
                }
                
                // 按创建时间排序（最新的在前）
                backups.sort { $0.createdAt > $1.createdAt }
                
                DispatchQueue.main.async {
                    self.availableBackups = backups
                }
            } catch {
                DispatchQueue.main.async {
                    self.lastError = "加载备份列表失败: \(error.localizedDescription)"
                }
            }
        }
    }
    
    // MARK: - 元数据管理
    
    private func saveBackupMetadata(_ backup: BackupInfo) throws {
        let metadataPath = "\(backupDirectory)/\(backup.filename).meta"
        let data = try JSONEncoder().encode(backup)
        try data.write(to: URL(fileURLWithPath: metadataPath))
    }
    
    private func deleteBackupMetadata(_ backup: BackupInfo) throws {
        let metadataPath = "\(backupDirectory)/\(backup.filename).meta"
        if FileManager.default.fileExists(atPath: metadataPath) {
            try FileManager.default.removeItem(atPath: metadataPath)
        }
    }
    
    // MARK: - 安全验证
    
    func requiresSecurityCheck(for action: SecurityAction) -> Bool {
        return action.requiresConfirmation
    }
    
    func validateAction(_ action: SecurityAction) -> [String] {
        var warnings: [String] = []
        
        switch action {
        case .startBot:
            // 检查是否有其他Bot进程在运行
            if checkExistingBotProcess() {
                warnings.append("检测到其他Bot进程正在运行")
            }
            
        case .stopBot, .restartBot:
            // 检查是否有未完成的交易
            warnings.append("请确保没有重要的未完成交易")
            
        case .resetSettings:
            warnings.append("重置后所有自定义设置将丢失")
            
        case .restoreBackup:
            warnings.append("当前设置将被覆盖")
            
        default:
            break
        }
        
        return warnings
    }
    
    private func checkExistingBotProcess() -> Bool {
        let task = Process()
        task.executableURL = URL(fileURLWithPath: "/bin/ps")
        task.arguments = ["aux"]
        
        let pipe = Pipe()
        task.standardOutput = pipe
        
        do {
            try task.run()
            task.waitUntilExit()
            
            let data = pipe.fileHandleForReading.readDataToEndOfFile()
            if let output = String(data: data, encoding: .utf8) {
                return output.contains("python") && output.contains("bot.py")
            }
        } catch {
            print("检查进程失败: \(error)")
        }
        
        return false
    }
}

// MARK: - 错误类型

enum SecurityError: LocalizedError {
    case backupNotFound
    case invalidBackup
    case permissionDenied
    
    var errorDescription: String? {
        switch self {
        case .backupNotFound:
            return "备份文件不存在"
        case .invalidBackup:
            return "备份文件无效"
        case .permissionDenied:
            return "权限不足"
        }
    }
}

// MARK: - Extensions

extension DateFormatter {
    static let backup: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter
    }()
    
    static let backupFilename: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyyMMdd_HHmmss"
        return formatter
    }()
}

extension ByteCountFormatter {
    static let shared: ByteCountFormatter = {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB]
        formatter.countStyle = .file
        return formatter
    }()
}
