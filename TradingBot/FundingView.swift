//
//  FundingView.swift
//  TradingBot
//
//  Created by Assistant on 2025-01-21.
//

import SwiftUI

struct FundingView: View {
    @ObservedObject var databaseManager: DatabaseManager
    @StateObject private var fundingManager: FundingManager

    init(databaseManager: DatabaseManager) {
        self.databaseManager = databaseManager
        self._fundingManager = StateObject(wrappedValue: FundingManager(databaseManager: databaseManager))
    }

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 错误提示
                if let errorMessage = fundingManager.errorMessage {
                    ErrorBannerView(message: errorMessage) {
                        fundingManager.errorMessage = nil
                    }
                }

                // 加载状态
                if fundingManager.isLoading && fundingManager.cryptoAccounts.isEmpty {
                    LoadingView()
                } else {
                    // 总价值概览
                    TotalValueOverviewView(fundingManager: fundingManager)

                    // 币种列表
                    CryptoAccountsView(fundingManager: fundingManager)
                }

                // 最后更新时间
                LastUpdateView(fundingManager: fundingManager)
            }
            .padding()
        }
        .navigationTitle("资金")
        .onAppear {
            fundingManager.loadAccountData()
        }
        .refreshable {
            await fundingManager.refreshAccountData()
        }
        .toolbar {
            ToolbarItem(placement: .automatic) {
                HStack {
                    if fundingManager.isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                    }

                    Button(action: {
                        Task {
                            await fundingManager.refreshAccountData()
                        }
                    }) {
                        Image(systemName: "arrow.clockwise")
                    }
                    .disabled(fundingManager.isLoading)
                }
            }
        }
    }
}

// MARK: - 总价值概览
struct TotalValueOverviewView: View {
    @ObservedObject var fundingManager: FundingManager

    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            // 顶部导航栏
            HStack {
                Text("Overview")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Spacer()
            }

            // 总价值显示
            VStack(alignment: .leading, spacing: 12) {
                HStack(alignment: .firstTextBaseline) {
                    Text("Est. Total Value")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Button(action: {}) {
                        Image(systemName: "eye.slash")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()
                }

                HStack(alignment: .firstTextBaseline, spacing: 8) {
                    Text(fundingManager.formatCurrency(fundingManager.totalValue))
                        .font(.system(size: 48, weight: .bold, design: .default))
                        .foregroundColor(.primary)

                    Text("USDT")
                        .font(.title2)
                        .foregroundColor(.secondary)
                        .padding(.top, 8)

                    Button(action: {}) {
                        Image(systemName: "chevron.down")
                            .font(.title3)
                            .foregroundColor(.secondary)
                    }
                    .padding(.top, 8)
                }

                Text("≈ $\(fundingManager.formatCurrency(fundingManager.totalValue))")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding(24)
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }
}





// MARK: - 币种账户列表
struct CryptoAccountsView: View {
    @ObservedObject var fundingManager: FundingManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Crypto")
                    .font(.title2)
                    .fontWeight(.semibold)

                Spacer()
                

            }
            
            // 币种列表
            LazyVStack(spacing: 12) {
                ForEach(fundingManager.cryptoAccounts, id: \.symbol) { account in
                    CryptoAccountRowView(account: account)
                }
            }
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }
}

// MARK: - 币种行视图
struct CryptoAccountRowView: View {
    let account: CryptoAccount

    var body: some View {
        HStack(spacing: 16) {
            // 币种图标和名称
            HStack(spacing: 12) {
                AsyncImage(url: URL(string: account.iconURL)) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                } placeholder: {
                    Circle()
                        .fill(Color.gray.opacity(0.3))
                        .overlay(
                            Text(String(account.symbol.prefix(2)))
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.white)
                        )
                }
                .frame(width: 40, height: 40)
                .clipShape(Circle())

                VStack(alignment: .leading, spacing: 4) {
                    Text(account.symbol)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.primary)

                    Text(account.name)
                        .font(.system(size: 14))
                        .foregroundColor(.secondary)
                }
            }

            Spacer()

            // 数量和价值
            VStack(alignment: .trailing, spacing: 2) {
                Text("\(account.formattedBalance) \(account.symbol)")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.primary)

                Text("\(account.formattedUSDTValue) USDT")
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
            }


        }
        .padding(.vertical, 12)
        .padding(.horizontal, 4)
    }
}

// MARK: - 错误横幅
struct ErrorBannerView: View {
    let message: String
    let onDismiss: () -> Void

    var body: some View {
        HStack {
            Image(systemName: "exclamationmark.triangle.fill")
                .foregroundColor(.orange)

            Text(message)
                .font(.subheadline)
                .foregroundColor(.primary)

            Spacer()

            Button(action: onDismiss) {
                Image(systemName: "xmark")
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color.orange.opacity(0.1))
        .cornerRadius(8)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(Color.orange.opacity(0.3), lineWidth: 1)
        )
    }
}

// MARK: - 加载视图
struct LoadingView: View {
    var body: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)

            Text("正在加载资金数据...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, minHeight: 200)
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }
}

// MARK: - 最后更新时间
struct LastUpdateView: View {
    @ObservedObject var fundingManager: FundingManager

    var body: some View {
        HStack {
            Text("最后更新: \(formatUpdateTime(fundingManager.lastUpdateTime))")
                .font(.caption)
                .foregroundColor(.secondary)

            Spacer()

            HStack(spacing: 4) {
                Circle()
                    .fill(Color.green)
                    .frame(width: 6, height: 6)

                Text("数据有效")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 4)
    }

    private func formatUpdateTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .medium
        return formatter.string(from: date)
    }
}

#Preview {
    FundingView(databaseManager: DatabaseManager())
}
