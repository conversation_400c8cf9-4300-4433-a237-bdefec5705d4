#!/usr/bin/env python3
"""
安全的手动修复：只移除最关键的引用，保持项目文件结构完整
"""

import os
import re
from datetime import datetime

def safe_manual_fix():
    project_root = "/Users/<USER>/my_projects/TradingBot"
    project_file = os.path.join(project_root, "TradingBot.xcodeproj", "project.pbxproj")
    
    print(f"🔧 安全修复Xcode项目文件: {project_file}")
    
    # 备份当前文件
    backup_file = f"{project_file}.safe_backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    with open(project_file, 'r') as f:
        content = f.read()
    
    with open(backup_file, 'w') as f:
        f.write(content)
    print(f"💾 已备份到: {backup_file}")
    
    # 只做最小的必要修改
    lines = content.split('\n')
    new_lines = []
    
    for line in lines:
        # 只移除明显的Python库路径，保持其他结构不变
        if any(pattern in line for pattern in [
            'site-packages/numpy',
            'site-packages/cryptography',
            'venv/lib/python3.13'
        ]):
            print(f"🗑️ 移除Python库路径: {line.strip()}")
            continue
        
        new_lines.append(line)
    
    # 写入修复后的文件
    new_content = '\n'.join(new_lines)
    with open(project_file, 'w') as f:
        f.write(new_content)
    
    print("✅ 安全修复完成！")
    
    # 验证文件是否有效
    try:
        # 简单检查文件结构
        if 'PBXProject' in new_content and 'rootObject' in new_content:
            print("✅ 项目文件结构验证通过")
        else:
            print("❌ 项目文件结构可能有问题")
    except Exception as e:
        print(f"❌ 验证失败: {e}")
    
    # 清理缓存
    print("🗑️ 清理Xcode缓存...")
    os.system("rm -rf ~/Library/Developer/Xcode/DerivedData/TradingBot-*")
    
    print("\n📋 现在请尝试打开Xcode项目")

if __name__ == "__main__":
    safe_manual_fix()
