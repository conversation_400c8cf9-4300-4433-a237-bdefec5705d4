# Xcode
*.xcodeproj/*
!*.xcodeproj/project.pbxproj
!*.xcodeproj/xcshareddata/
!*.xcodeproj/project.xcworkspace/
*.xcworkspace/*
!*.xcworkspace/contents.xcworkspacedata
!*.xcworkspace/xcshareddata/

# Build products
build/
DerivedData/
*.ipa
*.app

# User settings
*.pbxuser
*.mode1v3
*.mode2v3
*.perspectivev3
xcuserdata/

# Provisioning
*.mobileprovision
*.p12
*.cer
*.certSigningRequest

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
*.egg-info/
.venv/
env/
venv/

# 敏感配置文件
APIConfig.plist
config.py
dontshare_config.py
settings.json
*.key
*.pem

# 数据文件
*.db
*.db-shm
*.db-wal
*.json
!export_options.plist
!package.json

# 日志文件
*.log
*.log.*
log/
logs/

# 备份文件
backup/
*.backup
*.bak
*_backup_*

# 临时文件
*.tmp
*.temp
.DS_Store
.vscode/
.idea/
*.swp
*.swo
*~

# 测试覆盖率
.coverage
htmlcov/
.pytest_cache/

# 项目特定忽略
bot_4.2/data/
bot_4.2/log/
bot_4.2/backup/
bot_4.2/__pycache__/
bot_4.2/settings.json
bot_4.2/dontshare_config.py

# 独立的Git仓库
bot_4.2/
