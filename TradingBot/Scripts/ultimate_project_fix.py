#!/usr/bin/env python3
"""
终极修复：完全移除bot_4.2目录的所有引用
"""

import os
import re
from datetime import datetime

def ultimate_fix_xcode_project():
    project_root = "/Users/<USER>/my_projects/TradingBot"
    project_file = os.path.join(project_root, "TradingBot.xcodeproj", "project.pbxproj")
    
    print(f"🔧 终极修复Xcode项目文件: {project_file}")
    
    # 备份原文件
    backup_file = f"{project_file}.ultimate_backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    with open(project_file, 'r') as f:
        content = f.read()
    
    with open(backup_file, 'w') as f:
        f.write(content)
    print(f"💾 已备份到: {backup_file}")
    
    # 1. 移除bot_4.2的文件系统同步组定义
    print("🗑️ 移除bot_4.2文件系统同步组定义...")
    content = re.sub(
        r'96DA19632E2DF19700B2AE07 /\* bot_4\.2 \*/ = \{[^}]+\};',
        '',
        content,
        flags=re.DOTALL
    )
    
    # 2. 从fileSystemSynchronizedGroups中移除bot_4.2引用
    print("🗑️ 从fileSystemSynchronizedGroups中移除bot_4.2...")
    content = re.sub(
        r'fileSystemSynchronizedGroups = \(\s*96DA12EF2E2CA66F00B2AE07 /\* TradingBot \*/,\s*96DA19632E2DF19700B2AE07 /\* bot_4\.2 \*/,\s*\);',
        'fileSystemSynchronizedGroups = (\n\t\t\t\t\t96DA12EF2E2CA66F00B2AE07 /* TradingBot */,\n\t\t\t\t);',
        content,
        flags=re.DOTALL
    )
    
    # 3. 从children中移除bot_4.2引用
    print("🗑️ 从children中移除bot_4.2...")
    content = re.sub(
        r'children = \(\s*96DA19632E2DF19700B2AE07 /\* bot_4\.2 \*/,\s*96DA12EF2E2CA66F00B2AE07 /\* TradingBot \*/,',
        'children = (\n\t\t\t\t\t96DA12EF2E2CA66F00B2AE07 /* TradingBot */,',
        content,
        flags=re.DOTALL
    )
    
    # 4. 移除LIBRARY_SEARCH_PATHS中的Python路径
    print("🗑️ 清理LIBRARY_SEARCH_PATHS...")
    content = re.sub(
        r'LIBRARY_SEARCH_PATHS = \([^)]+\);',
        'LIBRARY_SEARCH_PATHS = (\n\t\t\t\t\t"$(inherited)",\n\t\t\t\t);',
        content,
        flags=re.DOTALL
    )
    
    # 5. 移除任何剩余的bot_4.2引用
    lines = content.split('\n')
    new_lines = []
    
    for line in lines:
        if any(pattern in line for pattern in ['bot_4.2', 'venv/lib/python', 'site-packages', 'numpy', 'cryptography']):
            print(f"🗑️ 移除行: {line.strip()}")
            continue
        new_lines.append(line)
    
    # 写入修复后的文件
    new_content = '\n'.join(new_lines)
    with open(project_file, 'w') as f:
        f.write(new_content)
    
    # 验证结果
    bot_refs = len([line for line in new_lines if 'bot_4.2' in line])
    python_refs = len([line for line in new_lines if any(pattern in line for pattern in ['.py', 'venv', 'numpy', 'site-packages'])])
    
    print(f"📊 剩余bot_4.2引用: {bot_refs}")
    print(f"📊 剩余Python引用: {python_refs}")
    print("✅ 项目文件终极修复完成！")
    
    # 清理缓存
    print("🗑️ 清理Xcode缓存...")
    os.system("rm -rf ~/Library/Developer/Xcode/DerivedData/TradingBot-*")
    
    print("\n📋 现在请：")
    print("1. 关闭Xcode（如果正在运行）")
    print("2. 重新打开Xcode项目")
    print("3. 检查项目导航器中是否还有bot_4.2文件夹")
    print("4. Product → Clean Build Folder (⇧⌘K)")
    print("5. Product → Build (⌘B)")

if __name__ == "__main__":
    ultimate_fix_xcode_project()
