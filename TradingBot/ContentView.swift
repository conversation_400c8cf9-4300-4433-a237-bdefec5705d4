//
//  ContentView.swift
//  TradingBot
//
//  Created by jason on 20/7/2025.
//

import SwiftUI

struct ContentView: View {
    @EnvironmentObject var databaseManager: DatabaseManager
    @State private var selectedTab = 0

    // 持久化的View实例，避免重复创建
    @StateObject private var botControlView = TempBotControlViewWrapper()
    @StateObject private var settingsView = TempSettingsViewWrapper()

    var body: some View {
        NavigationSplitView {
            // 侧边栏
            SidebarView(selectedTab: $selectedTab)
        } detail: {
            // 主内容区
            Group {
                switch selectedTab {
                case 0:
                    DashboardView(databaseManager: databaseManager)
                case 1:
                    ProfitAnalysisView(databaseManager: databaseManager)
                case 2:
                    FundingView(databaseManager: databaseManager)
                case 3:
                    SystemView(databaseManager: databaseManager)
                case 4:
                    botControlView.view
                case 5:
                    settingsView.view
                case 6:
                    TempSecurityView()
                default:
                    DashboardView(databaseManager: databaseManager)
                }
            }
        }
        .frame(minWidth: 1000, minHeight: 700)
    }
}

struct SidebarView: View {
    @Binding var selectedTab: Int

    var body: some View {
        List(selection: $selectedTab) {
            NavigationLink(value: 0) {
                Label("仪表板", systemImage: "chart.line.uptrend.xyaxis")
            }
            .tag(0)

            NavigationLink(value: 1) {
                Label("收益分析", systemImage: "dollarsign.circle")
            }
            .tag(1)

            NavigationLink(value: 2) {
                Label("资金", systemImage: "creditcard.fill")
            }
            .tag(2)

            NavigationLink(value: 3) {
                Label("系统状态", systemImage: "gear")
            }
            .tag(3)

            NavigationLink(value: 4) {
                Label("Bot控制", systemImage: "play.circle.fill")
            }
            .tag(4)

            NavigationLink(value: 5) {
                Label("设置", systemImage: "slider.horizontal.3")
            }
            .tag(5)

            NavigationLink(value: 6) {
                Label("安全", systemImage: "lock.shield")
            }
            .tag(6)
        }
        .navigationTitle("交易机器人")
        .frame(minWidth: 200)
    }
}

struct SystemView: View {
    @ObservedObject var databaseManager: DatabaseManager
    @State private var systemInfo = SystemInfo()

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 数据库连接状态
                DatabaseStatusCard(databaseManager: databaseManager)

                // 系统资源监控
                SystemResourcesCard(systemInfo: systemInfo)

                // 应用信息
                AppInfoCard()

                // 日志查看器
                LogViewerCard()
            }
            .padding()
        }
        .navigationTitle("系统状态")
        .onAppear {
            updateSystemInfo()
            // 定时更新系统信息
            Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { _ in
                updateSystemInfo()
            }
        }
    }

    private func updateSystemInfo() {
        systemInfo.update()
    }
}

struct DatabaseStatusCard: View {
    @ObservedObject var databaseManager: DatabaseManager

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("数据库状态")
                .font(.headline)

            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    StatusItem(title: "连接状态", value: databaseManager.isConnected ? "已连接" : "未连接",
                              color: databaseManager.isConnected ? .green : .red)
                    StatusItem(title: "活跃Parts", value: "\(databaseManager.activeParts)", color: .blue)
                    StatusItem(title: "总交易次数", value: "\(databaseManager.totalTrades)", color: .orange)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 8) {
                    StatusItem(title: "最后更新", value: timeAgo(databaseManager.lastUpdate), color: .secondary)
                    StatusItem(title: "数据源", value: "SQLite", color: .green)
                    StatusItem(title: "实时更新", value: "每5秒", color: .blue)
                }
            }
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }

    private func timeAgo(_ date: Date) -> String {
        let interval = Date().timeIntervalSince(date)
        if interval < 60 {
            return "\(Int(interval))秒前"
        } else {
            return "\(Int(interval/60))分钟前"
        }
    }
}

struct SystemResourcesCard: View {
    let systemInfo: SystemInfo

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("系统资源")
                .font(.headline)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                ResourceMeter(title: "CPU使用率", value: systemInfo.cpuUsage, unit: "%", color: .blue)
                ResourceMeter(title: "内存使用率", value: systemInfo.memoryUsage, unit: "%", color: .green)
                ResourceMeter(title: "可用内存", value: systemInfo.availableMemory, unit: "GB", color: .orange)
                ResourceMeter(title: "磁盘使用率", value: systemInfo.diskUsage, unit: "%", color: .purple)
            }
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }
}

struct ResourceMeter: View {
    let title: String
    let value: Double
    let unit: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)

            Text("\(String(format: "%.1f", value))\(unit)")
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(color)

            ProgressView(value: value, total: 100)
                .progressViewStyle(LinearProgressViewStyle(tint: color))
        }
        .padding()
        .background(Color(.secondarySystemFill))
        .cornerRadius(8)
    }
}

struct AppInfoCard: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("应用信息")
                .font(.headline)

            VStack(alignment: .leading, spacing: 8) {
                InfoRow(title: "应用版本", value: "1.0.0")
                InfoRow(title: "构建版本", value: "1")
                InfoRow(title: "Swift版本", value: "5.9")
                InfoRow(title: "系统版本", value: ProcessInfo.processInfo.operatingSystemVersionString)
                InfoRow(title: "存储模式", value: "SQLite Only")
            }
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }
}

struct StatusItem: View {
    let title: String
    let value: String
    let color: Color

    var body: some View {
        HStack {
            Text(title + ":")
                .font(.caption)
                .foregroundColor(.secondary)
            Text(value)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(color)
        }
    }
}

struct InfoRow: View {
    let title: String
    let value: String

    var body: some View {
        HStack {
            Text(title)
                .font(.body)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .font(.body)
                .fontWeight(.medium)
        }
    }
}

class SystemInfo: ObservableObject {
    @Published var cpuUsage: Double = 0
    @Published var memoryUsage: Double = 0
    @Published var availableMemory: Double = 0
    @Published var diskUsage: Double = 0

    func update() {
        // 简化的系统信息获取
        // 实际应用中可以使用更精确的系统API
        cpuUsage = Double.random(in: 5...25) // 模拟CPU使用率
        memoryUsage = Double.random(in: 30...70) // 模拟内存使用率
        availableMemory = Double.random(in: 2...8) // 模拟可用内存
        diskUsage = Double.random(in: 40...80) // 模拟磁盘使用率
    }
}

struct LogViewerCard: View {
    @State private var showingFullLogViewer = false

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("日志监控")
                    .font(.headline)

                Spacer()

                Button("查看详细日志") {
                    showingFullLogViewer = true
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
            }

            Text("实时监控Bot运行日志和系统状态")
                .font(.caption)
                .foregroundColor(.secondary)

            HStack {
                Label("实时监控", systemImage: "eye")
                    .font(.caption)
                    .foregroundColor(.green)

                Spacer()

                Label("自动刷新", systemImage: "arrow.clockwise")
                    .font(.caption)
                    .foregroundColor(.blue)
            }
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
        .sheet(isPresented: $showingFullLogViewer) {
            NavigationView {
                LogViewerView()
                    .toolbar {
                        ToolbarItem(placement: .cancellationAction) {
                            Button("关闭") {
                                showingFullLogViewer = false
                            }
                        }
                    }
            }
            .frame(minWidth: 800, minHeight: 600)
        }
    }
}

// MARK: - 临时View实现

// Wrapper类，用于持久化View实例
class TempBotControlViewWrapper: ObservableObject {
    private var _view: TempBotControlView?

    var view: TempBotControlView {
        if _view == nil {
            print("🔧 创建新的 TempBotControlView 实例")
            _view = TempBotControlView()
        }
        return _view!
    }
}

class TempSettingsViewWrapper: ObservableObject {
    lazy var view = TempSettingsView()
}

struct TempBotControlView: View {
    @StateObject private var processManager = BotProcessManager()
    @State private var showingPathInfo = false
    @State private var isViewLoaded = false

    var body: some View {
        VStack(spacing: 20) {
            // 加载状态指示
            if !isViewLoaded {
                VStack {
                    ProgressView("正在初始化Bot控制...")
                        .padding()
                    Text("请稍候...")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                // 简化的状态显示
                Text("Bot状态: \(processManager.status.displayText)")
                    .font(.title2)
                    .padding()

            // 简化的控制按钮
            HStack(spacing: 12) {
                Button("启动Bot") {
                    processManager.startBot()
                }
                .buttonStyle(.borderedProminent)

                Button("停止Bot") {
                    processManager.stopBot()
                }
                .buttonStyle(.bordered)

                Button("路径信息") {
                    showingPathInfo = true
                }
                .buttonStyle(.bordered)
            }
            .padding()

            // 简化的日志显示
            VStack(alignment: .leading) {
                Text("日志 (\(processManager.logs.count) 条)")
                    .font(.headline)

                ScrollView {
                    LazyVStack(alignment: .leading, spacing: 2) {
                        ForEach(Array(processManager.logs.enumerated()), id: \.offset) { index, log in
                            Text(log)
                                .font(.system(.caption, design: .monospaced))
                                .textSelection(.enabled)
                        }
                    }
                }
                .frame(height: 200)
                .background(Color.gray.opacity(0.1))
                .cornerRadius(8)
            }
            .padding()

                Spacer()
            }
        }
        .navigationTitle("Bot控制")
        .onAppear {
            print("🔍 TempBotControlView onAppear 开始")

            // 延迟加载，确保不阻塞主线程
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                print("🔍 TempBotControlView 延迟加载完成")
                isViewLoaded = true
                print("🔍 ProcessManager status: \(processManager.status)")
                print("🔍 Bot directory: \(processManager.botDirectoryPath)")
            }
        }
        .sheet(isPresented: $showingPathInfo) {
            VStack(alignment: .leading, spacing: 16) {
                Text("路径信息")
                    .font(.headline)

                VStack(alignment: .leading, spacing: 8) {
                    Text("Bot目录:")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    Text(processManager.botDirectoryPath)
                        .font(.caption)
                        .textSelection(.enabled)
                        .padding(8)
                        .background(Color(.controlBackgroundColor))
                        .cornerRadius(4)

                    Text("Python路径:")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    Text("/usr/bin/python3")
                        .font(.caption)
                        .textSelection(.enabled)
                        .padding(8)
                        .background(Color(.controlBackgroundColor))
                        .cornerRadius(4)
                }

                Button("关闭") {
                    showingPathInfo = false
                }
                .buttonStyle(.borderedProminent)
            }
            .padding()
            .frame(width: 400, height: 250)
        }
    }
}

struct TempSettingsView: View {
    @StateObject private var settingsManager = SettingsManager()
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var logs: [String] = []

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 状态信息
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Text("设置状态")
                            .font(.headline)

                        Spacer()

                        if settingsManager.isLoading {
                            ProgressView()
                                .controlSize(.small)
                        } else {
                            Circle()
                                .fill(settingsManager.lastError == nil ? .green : .red)
                                .frame(width: 12, height: 12)
                        }
                    }

                    Text("配置文件: settings.json")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .textSelection(.enabled)

                    if let error = settingsManager.lastError {
                        Text("错误: \(error)")
                            .font(.caption)
                            .foregroundColor(.red)
                    }
                }
                .padding()
                .background(Color(.controlBackgroundColor))
                .cornerRadius(12)

                // 交易设置
                VStack(alignment: .leading, spacing: 12) {
                    Text("交易设置")
                        .font(.headline)

                    VStack(alignment: .leading, spacing: 8) {
                        Text("交易对")
                            .font(.subheadline)
                            .fontWeight(.medium)

                        Picker("交易对", selection: $settingsManager.settings.trading.symbol) {
                            ForEach(settingsManager.supportedSymbols, id: \.self) { symbol in
                                Text(symbol).tag(symbol)
                            }
                        }
                        .pickerStyle(.menu)
                        .onChange(of: settingsManager.settings.trading.symbol) { _, newValue in
                            addLog("📝 交易对更改为: \(newValue)")
                        }
                    }

                    Toggle("模拟交易", isOn: $settingsManager.settings.trading.simulatedTrading)
                        .help("开启后将使用测试网进行模拟交易")
                        .onChange(of: settingsManager.settings.trading.simulatedTrading) { _, newValue in
                            addLog("📝 模拟交易设置: \(newValue ? "开启" : "关闭")")
                        }

                    // API配置区域
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("🔑 API配置")
                                .font(.subheadline)
                                .fontWeight(.medium)

                            Spacer()

                            Button("配置") {
                                addLog("🔧 API配置功能开发中...")
                            }
                            .buttonStyle(.bordered)
                        }

                        // API状态显示
                        HStack {
                            Text("生产环境:")
                                .font(.caption)
                                .frame(width: 70, alignment: .leading)

                            if !settingsManager.settings.trading.api.production.apiKey.isEmpty {
                                Label("已配置", systemImage: "checkmark.circle.fill")
                                    .font(.caption)
                                    .foregroundColor(.green)
                            } else {
                                Label("未配置", systemImage: "exclamationmark.circle.fill")
                                    .font(.caption)
                                    .foregroundColor(.orange)
                            }

                            Spacer()
                        }

                        HStack {
                            Text("测试环境:")
                                .font(.caption)
                                .frame(width: 70, alignment: .leading)

                            if !settingsManager.settings.trading.api.testnet.apiKey.isEmpty {
                                Label("已配置", systemImage: "checkmark.circle.fill")
                                    .font(.caption)
                                    .foregroundColor(.green)
                            } else {
                                Label("未配置", systemImage: "exclamationmark.circle.fill")
                                    .font(.caption)
                                    .foregroundColor(.orange)
                            }

                            Spacer()
                        }
                    }
                }
                .padding()
                .background(Color(.controlBackgroundColor))
                .cornerRadius(12)

                // 策略设置
                VStack(alignment: .leading, spacing: 12) {
                    Text("策略设置")
                        .font(.headline)

                    VStack(alignment: .leading, spacing: 8) {
                        Text("Part资金: \(String(format: "%.0f", settingsManager.settings.strategy.partFunds.current)) USDT")
                            .font(.subheadline)
                            .fontWeight(.medium)

                        Slider(value: $settingsManager.settings.strategy.partFunds.current, in: 10...1000, step: 10)
                            .onChange(of: settingsManager.settings.strategy.partFunds.current) { _, newValue in
                                addLog("📝 Part资金更改为: \(String(format: "%.0f", newValue)) USDT")
                            }
                    }

                    VStack(alignment: .leading, spacing: 8) {
                        Text("止盈率: \(String(format: "%.3f", settingsManager.settings.strategy.tpRate))")
                            .font(.subheadline)
                            .fontWeight(.medium)

                        Slider(value: $settingsManager.settings.strategy.tpRate, in: 1.001...1.1, step: 0.001)
                            .onChange(of: settingsManager.settings.strategy.tpRate) { _, newValue in
                                addLog("📝 止盈率更改为: \(String(format: "%.3f", newValue))")
                            }
                    }
                }
                .padding()
                .background(Color(.controlBackgroundColor))
                .cornerRadius(12)

                // 操作按钮
                VStack(spacing: 12) {
                    HStack(spacing: 12) {
                        Button("加载设置") {
                            loadSettings()
                        }
                        .buttonStyle(.bordered)
                        .disabled(settingsManager.isLoading)

                        Button("重置为默认") {
                            resetToDefaults()
                        }
                        .buttonStyle(.bordered)
                        .disabled(settingsManager.isLoading)

                        Spacer()

                        Button("保存设置") {
                            saveSettings()
                        }
                        .buttonStyle(.borderedProminent)
                        .disabled(settingsManager.isLoading)
                    }

                    // 快速操作按钮
                    HStack(spacing: 8) {
                        Button("查看配置文件") {
                            viewConfigFile()
                        }
                        .buttonStyle(.bordered)
                        .controlSize(.small)

                        Button("备份设置") {
                            backupSettings()
                        }
                        .buttonStyle(.bordered)
                        .controlSize(.small)

                        Spacer()
                    }
                }
                .padding()

                // 日志显示
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Text("操作日志")
                            .font(.headline)

                        Spacer()

                        Button("清空日志") {
                            logs.removeAll()
                            addLog("🧹 日志已清空")
                        }
                        .buttonStyle(.bordered)
                        .controlSize(.small)
                    }

                    ScrollView {
                        LazyVStack(alignment: .leading, spacing: 2) {
                            if logs.isEmpty {
                                Text("暂无操作日志...")
                                    .foregroundColor(.secondary)
                                    .italic()
                                    .padding()
                            } else {
                                ForEach(Array(logs.enumerated()), id: \.offset) { index, log in
                                    Text(log)
                                        .font(.system(.caption, design: .monospaced))
                                        .textSelection(.enabled)
                                        .padding(.vertical, 1)
                                }
                            }
                        }
                        .padding(.horizontal, 8)
                    }
                    .frame(height: 150)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                }
                .padding()
                .background(Color(.controlBackgroundColor))
                .cornerRadius(12)
            }
            .padding()
        }
        .navigationTitle("设置")
        .onAppear {
            print("🔧 TempSettingsView appeared")
            addLog("🔧 设置页面已加载")
            print("🔧 SettingsManager isLoading: \(settingsManager.isLoading)")
            print("🔧 SettingsManager lastError: \(settingsManager.lastError ?? "nil")")
            loadSettings()
        }
        .alert("提示", isPresented: $showingAlert) {
            Button("确定") { }
        } message: {
            Text(alertMessage)
        }
    }

    // MARK: - 功能方法

    private func addLog(_ message: String) {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"
        let timestamp = formatter.string(from: Date())
        let logEntry = "[\(timestamp)] \(message)"
        logs.append(logEntry)

        // 限制日志数量
        if logs.count > 100 {
            logs.removeFirst(logs.count - 100)
        }

        print("📝 Settings: \(logEntry)")
    }

    private func loadSettings() {
        addLog("📥 开始加载设置...")
        settingsManager.loadSettings()

        // 监听加载结果
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            if let error = settingsManager.lastError {
                addLog("❌ 设置加载失败: \(error)")
                alertMessage = "加载设置失败：\(error)"
                showingAlert = true
            } else {
                addLog("✅ 设置加载成功")
                addLog("📊 交易对: \(settingsManager.settings.trading.symbol)")
                addLog("🎮 模拟交易: \(settingsManager.settings.trading.simulatedTrading ? "开启" : "关闭")")
                addLog("💰 Part资金: \(String(format: "%.0f", settingsManager.settings.strategy.partFunds.current)) USDT")
                addLog("📈 止盈率: \(String(format: "%.3f", settingsManager.settings.strategy.tpRate))")
            }
        }
    }

    private func saveSettings() {
        addLog("💾 开始保存设置...")
        settingsManager.saveSettings()

        // 监听保存结果
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            if let error = settingsManager.lastError {
                addLog("❌ 设置保存失败: \(error)")
                alertMessage = "保存设置失败：\(error)"
                showingAlert = true
            } else {
                addLog("✅ 设置保存成功")
                addLog("📁 保存到配置文件")
                alertMessage = "设置已成功保存到配置文件"
                showingAlert = true
            }
        }
    }

    private func resetToDefaults() {
        addLog("🔄 重置为默认设置")
        settingsManager.settings.trading.symbol = "BTC/USDT"
        settingsManager.settings.trading.simulatedTrading = true
        settingsManager.settings.strategy.partFunds.current = 40.0
        settingsManager.settings.strategy.tpRate = 1.002
        addLog("✅ 已重置为默认值")
    }

    private func viewConfigFile() {
        addLog("👀 查看配置文件目录")
        // 在Finder中显示Bot目录
        let botDir = "/Users/<USER>/my_projects/TradingBot/bot_4.2"
        NSWorkspace.shared.selectFile(nil, inFileViewerRootedAtPath: botDir)
    }

    private func backupSettings() {
        addLog("💾 创建设置备份...")

        DispatchQueue.global(qos: .userInitiated).async {
            do {
                let backupPath = try settingsManager.createBackup()

                DispatchQueue.main.async {
                    addLog("✅ 备份创建成功")
                    addLog("📁 备份位置: \(backupPath)")
                    alertMessage = "设置备份已创建：\n\(backupPath)"
                    showingAlert = true
                }
            } catch {
                DispatchQueue.main.async {
                    addLog("❌ 备份创建失败: \(error.localizedDescription)")
                    alertMessage = "创建备份失败：\(error.localizedDescription)"
                    showingAlert = true
                }
            }
        }
    }
}

struct TempSecurityView: View {
    @State private var requireConfirmation = true
    @State private var autoBackup = true
    @State private var showingCreateBackup = false

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 安全概览
                VStack(alignment: .leading, spacing: 12) {
                    Text("安全概览")
                        .font(.headline)

                    VStack(spacing: 8) {
                        HStack {
                            Image(systemName: "checkmark.shield")
                                .foregroundColor(.green)
                            Text("操作确认")
                            Spacer()
                            Text("已启用")
                                .font(.caption)
                                .foregroundColor(.green)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 2)
                                .background(Color.green.opacity(0.1))
                                .cornerRadius(4)
                        }

                        HStack {
                            Image(systemName: "archivebox")
                                .foregroundColor(.green)
                            Text("自动备份")
                            Spacer()
                            Text("已启用")
                                .font(.caption)
                                .foregroundColor(.green)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 2)
                                .background(Color.green.opacity(0.1))
                                .cornerRadius(4)
                        }
                    }
                }
                .padding()
                .background(Color(.controlBackgroundColor))
                .cornerRadius(12)

                // 备份管理
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Text("备份管理")
                            .font(.headline)

                        Spacer()

                        Button("创建备份") {
                            showingCreateBackup = true
                        }
                        .buttonStyle(.borderedProminent)
                    }

                    Text("暂无备份文件")
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity, minHeight: 100)
                        .background(Color(.controlBackgroundColor).opacity(0.5))
                        .cornerRadius(8)
                }
                .padding()
                .background(Color(.controlBackgroundColor))
                .cornerRadius(12)

                // 安全选项
                VStack(alignment: .leading, spacing: 12) {
                    Text("安全选项")
                        .font(.headline)

                    Toggle("操作前需要确认", isOn: $requireConfirmation)
                        .help("危险操作前显示确认对话框")

                    Toggle("自动创建备份", isOn: $autoBackup)
                        .help("保存设置前自动创建备份")
                }
                .padding()
                .background(Color(.controlBackgroundColor))
                .cornerRadius(12)
            }
            .padding()
        }
        .navigationTitle("安全设置")
        .alert("创建备份", isPresented: $showingCreateBackup) {
            Button("取消", role: .cancel) { }
            Button("创建") {
                // 创建备份逻辑
            }
        } message: {
            Text("确定要创建当前设置的备份吗？")
        }
    }
}

#Preview {
    ContentView()
        .environmentObject(DatabaseManager())
}
