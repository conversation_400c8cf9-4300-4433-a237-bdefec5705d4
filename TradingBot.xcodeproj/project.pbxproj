// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		96DA12FC2E2CA67000B2AE07 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 96DA12E52E2CA66F00B2AE07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 96DA12EC2E2CA66F00B2AE07;
			remoteInfo = TradingBot;
		};
		96DA13062E2CA67000B2AE07 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 96DA12E52E2CA66F00B2AE07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 96DA12EC2E2CA66F00B2AE07;
			remoteInfo = TradingBot;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		96DA12ED2E2CA66F00B2AE07 /* TradingBot.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = TradingBot.app; sourceTree = BUILT_PRODUCTS_DIR; };
		96DA12FB2E2CA67000B2AE07 /* TradingBotTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = TradingBotTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		96DA13052E2CA67000B2AE07 /* TradingBotUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = TradingBotUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		96DA12EF2E2CA66F00B2AE07 /* TradingBot */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = TradingBot;
			sourceTree = "<group>";
		};
		96DA12FE2E2CA67000B2AE07 /* TradingBotTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = TradingBotTests;
			sourceTree = "<group>";
		};
		96DA13082E2CA67000B2AE07 /* TradingBotUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = TradingBotUITests;
			sourceTree = "<group>";
		};

/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		96DA12EA2E2CA66F00B2AE07 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		96DA12F82E2CA67000B2AE07 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		96DA13022E2CA67000B2AE07 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		96DA12E42E2CA66F00B2AE07 = {
			isa = PBXGroup;
			children = (
				96DA12EF2E2CA66F00B2AE07 /* TradingBot */,
				96DA12FE2E2CA67000B2AE07 /* TradingBotTests */,
				96DA13082E2CA67000B2AE07 /* TradingBotUITests */,
				96DA12EE2E2CA66F00B2AE07 /* Products */,
			);
			sourceTree = "<group>";
		};
		96DA12EE2E2CA66F00B2AE07 /* Products */ = {
			isa = PBXGroup;
			children = (
				96DA12ED2E2CA66F00B2AE07 /* TradingBot.app */,
				96DA12FB2E2CA67000B2AE07 /* TradingBotTests.xctest */,
				96DA13052E2CA67000B2AE07 /* TradingBotUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		96DA12EC2E2CA66F00B2AE07 /* TradingBot */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 96DA130F2E2CA67000B2AE07 /* Build configuration list for PBXNativeTarget "TradingBot" */;
			buildPhases = (
				96DA12E92E2CA66F00B2AE07 /* Sources */,
				96DA12EA2E2CA66F00B2AE07 /* Frameworks */,
				96DA12EB2E2CA66F00B2AE07 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				96DA12EF2E2CA66F00B2AE07 /* TradingBot */,
			);
			name = TradingBot;
			packageProductDependencies = (
			);
			productName = TradingBot;
			productReference = 96DA12ED2E2CA66F00B2AE07 /* TradingBot.app */;
			productType = "com.apple.product-type.application";
		};
		96DA12FA2E2CA67000B2AE07 /* TradingBotTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 96DA13122E2CA67000B2AE07 /* Build configuration list for PBXNativeTarget "TradingBotTests" */;
			buildPhases = (
				96DA12F72E2CA67000B2AE07 /* Sources */,
				96DA12F82E2CA67000B2AE07 /* Frameworks */,
				96DA12F92E2CA67000B2AE07 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				96DA12FD2E2CA67000B2AE07 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				96DA12FE2E2CA67000B2AE07 /* TradingBotTests */,
			);
			name = TradingBotTests;
			packageProductDependencies = (
			);
			productName = TradingBotTests;
			productReference = 96DA12FB2E2CA67000B2AE07 /* TradingBotTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		96DA13042E2CA67000B2AE07 /* TradingBotUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 96DA13152E2CA67000B2AE07 /* Build configuration list for PBXNativeTarget "TradingBotUITests" */;
			buildPhases = (
				96DA13012E2CA67000B2AE07 /* Sources */,
				96DA13022E2CA67000B2AE07 /* Frameworks */,
				96DA13032E2CA67000B2AE07 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				96DA13072E2CA67000B2AE07 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				96DA13082E2CA67000B2AE07 /* TradingBotUITests */,
			);
			name = TradingBotUITests;
			packageProductDependencies = (
			);
			productName = TradingBotUITests;
			productReference = 96DA13052E2CA67000B2AE07 /* TradingBotUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		96DA12E52E2CA66F00B2AE07 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					96DA12EC2E2CA66F00B2AE07 = {
						CreatedOnToolsVersion = 16.4;
					};
					96DA12FA2E2CA67000B2AE07 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 96DA12EC2E2CA66F00B2AE07;
					};
					96DA13042E2CA67000B2AE07 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 96DA12EC2E2CA66F00B2AE07;
					};
				};
			};
			buildConfigurationList = 96DA12E82E2CA66F00B2AE07 /* Build configuration list for PBXProject "TradingBot" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 96DA12E42E2CA66F00B2AE07;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 96DA12EE2E2CA66F00B2AE07 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				96DA12EC2E2CA66F00B2AE07 /* TradingBot */,
				96DA12FA2E2CA67000B2AE07 /* TradingBotTests */,
				96DA13042E2CA67000B2AE07 /* TradingBotUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		96DA12EB2E2CA66F00B2AE07 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		96DA12F92E2CA67000B2AE07 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		96DA13032E2CA67000B2AE07 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		96DA12E92E2CA66F00B2AE07 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		96DA12F72E2CA67000B2AE07 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		96DA13012E2CA67000B2AE07 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		96DA12FD2E2CA67000B2AE07 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 96DA12EC2E2CA66F00B2AE07 /* TradingBot */;
			targetProxy = 96DA12FC2E2CA67000B2AE07 /* PBXContainerItemProxy */;
		};
		96DA13072E2CA67000B2AE07 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 96DA12EC2E2CA66F00B2AE07 /* TradingBot */;
			targetProxy = 96DA13062E2CA67000B2AE07 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		96DA130D2E2CA67000B2AE07 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = ZT7FYB7TYK;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		96DA130E2E2CA67000B2AE07 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = ZT7FYB7TYK;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		96DA13102E2CA67000B2AE07 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = TradingBot/TradingBot.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = ZT7FYB7TYK;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = TradingDashboard.TradingBot;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		96DA13112E2CA67000B2AE07 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = TradingBot/TradingBot.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = ZT7FYB7TYK;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = TradingDashboard.TradingBot;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		96DA13132E2CA67000B2AE07 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = ZT7FYB7TYK;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = TradingDashboard.TradingBotTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/TradingBot.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/TradingBot";
			};
			name = Debug;
		};
		96DA13142E2CA67000B2AE07 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = ZT7FYB7TYK;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = TradingDashboard.TradingBotTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/TradingBot.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/TradingBot";
			};
			name = Release;
		};
		96DA13162E2CA67000B2AE07 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = ZT7FYB7TYK;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = TradingDashboard.TradingBotUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = TradingBot;
			};
			name = Debug;
		};
		96DA13172E2CA67000B2AE07 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = ZT7FYB7TYK;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = TradingDashboard.TradingBotUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = TradingBot;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		96DA12E82E2CA66F00B2AE07 /* Build configuration list for PBXProject "TradingBot" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				96DA130D2E2CA67000B2AE07 /* Debug */,
				96DA130E2E2CA67000B2AE07 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		96DA130F2E2CA67000B2AE07 /* Build configuration list for PBXNativeTarget "TradingBot" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				96DA13102E2CA67000B2AE07 /* Debug */,
				96DA13112E2CA67000B2AE07 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		96DA13122E2CA67000B2AE07 /* Build configuration list for PBXNativeTarget "TradingBotTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				96DA13132E2CA67000B2AE07 /* Debug */,
				96DA13142E2CA67000B2AE07 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		96DA13152E2CA67000B2AE07 /* Build configuration list for PBXNativeTarget "TradingBotUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				96DA13162E2CA67000B2AE07 /* Debug */,
				96DA13172E2CA67000B2AE07 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 96DA12E52E2CA66F00B2AE07 /* Project object */;
}
