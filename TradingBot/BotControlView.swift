//
//  BotControlView.swift
//  TradingBot
//
//  Created by Assistant on 2025-01-21.
//

import SwiftUI

struct BotControlView: View {
    @StateObject private var processManager = BotProcessManager()
    @State private var showingStopAlert = false
    @State private var showingRestartAlert = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 状态卡片
                StatusCard(processManager: processManager)
                
                // 控制按钮
                ControlButtons(
                    processManager: processManager,
                    showingStopAlert: $showingStopAlert,
                    showingRestartAlert: $showingRestartAlert
                )
                
                // 日志查看器
                LogViewer(processManager: processManager)
            }
            .padding()
        }
        .navigationTitle("Bot控制")
        .sheet(isPresented: $processManager.showingSecurityConfirmation) {
            if let action = processManager.pendingAction {
                SecurityConfirmationDialog(
                    action: action,
                    warnings: processManager.getSecurityWarnings(),
                    onConfirm: {
                        processManager.confirmSecurityAction()
                    },
                    onCancel: {
                        processManager.cancelSecurityAction()
                    }
                )
            }
        }
    }
}

// MARK: - 状态卡片

struct StatusCard: View {
    @ObservedObject var processManager: BotProcessManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Bot状态")
                .font(.headline)
            
            HStack(spacing: 20) {
                // 状态指示器
                VStack(alignment: .leading, spacing: 8) {
                    HStack(spacing: 8) {
                        Circle()
                            .fill(statusColor)
                            .frame(width: 12, height: 12)
                        Text(processManager.status.displayText)
                            .font(.title2)
                            .fontWeight(.semibold)
                    }
                    
                    Text("进程状态")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // 进程信息
                VStack(alignment: .trailing, spacing: 8) {
                    Text(processManager.isProcessRunning ? "进程运行中" : "进程已停止")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Text("实时监控")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // 状态详情
            if case .error(let message) = processManager.status {
                Text(message)
                    .font(.caption)
                    .foregroundColor(.red)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(4)
            }
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }
    
    private var statusColor: Color {
        switch processManager.status {
        case .stopped:
            return .gray
        case .starting, .stopping:
            return .orange
        case .running:
            return .green
        case .error:
            return .red
        }
    }
}

// MARK: - 控制按钮

struct ControlButtons: View {
    @ObservedObject var processManager: BotProcessManager
    @Binding var showingStopAlert: Bool
    @Binding var showingRestartAlert: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("控制操作")
                .font(.headline)
            
            HStack(spacing: 12) {
                // 启动按钮
                Button(action: {
                    processManager.startBot()
                }) {
                    HStack {
                        Image(systemName: "play.fill")
                        Text("启动Bot")
                    }
                    .frame(maxWidth: .infinity)
                }
                .buttonStyle(.borderedProminent)
                .disabled(processManager.status == .running || processManager.status == .starting)
                
                // 停止按钮
                Button(action: {
                    processManager.stopBot()
                }) {
                    HStack {
                        Image(systemName: "stop.fill")
                        Text("停止Bot")
                    }
                    .frame(maxWidth: .infinity)
                }
                .buttonStyle(.bordered)
                .foregroundColor(.red)
                .disabled(processManager.status == .stopped || processManager.status == .stopping)
                
                // 重启按钮
                Button(action: {
                    processManager.restartBot()
                }) {
                    HStack {
                        Image(systemName: "arrow.clockwise")
                        Text("重启Bot")
                    }
                    .frame(maxWidth: .infinity)
                }
                .buttonStyle(.bordered)
                .disabled(processManager.status == .starting || processManager.status == .stopping)
            }
            
            // 操作提示
            VStack(alignment: .leading, spacing: 4) {
                Text("操作说明:")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                
                Text("• 启动: 启动新的Bot进程")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text("• 停止: 优雅停止当前Bot进程")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text("• 重启: 停止当前进程并重新启动")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.top, 8)
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }
}

// MARK: - 日志查看器

struct LogViewer: View {
    @ObservedObject var processManager: BotProcessManager
    @State private var isAutoScroll = true
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("实时日志")
                    .font(.headline)
                
                Spacer()
                
                // 自动滚动开关
                Toggle("自动滚动", isOn: $isAutoScroll)
                    .toggleStyle(.switch)
                    .scaleEffect(0.8)
                
                // 清空日志按钮
                Button("清空") {
                    processManager.clearLogs()
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
            }
            
            // 日志内容
            ScrollViewReader { proxy in
                ScrollView {
                    LazyVStack(alignment: .leading, spacing: 2) {
                        if processManager.logs.isEmpty {
                            Text("暂无日志...")
                                .foregroundColor(.secondary)
                                .italic()
                                .padding()
                        } else {
                            ForEach(Array(processManager.logs.enumerated()), id: \.offset) { index, log in
                                BotLogEntry(text: log)
                                    .id(index)
                            }
                        }
                    }
                    .padding(.horizontal, 8)
                }
                .frame(height: 300)
                .background(Color.black.opacity(0.05))
                .cornerRadius(8)
                .onChange(of: processManager.logs.count) { _, _ in
                    if isAutoScroll && !processManager.logs.isEmpty {
                        withAnimation(.easeOut(duration: 0.3)) {
                            proxy.scrollTo(processManager.logs.count - 1, anchor: .bottom)
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }
}

// MARK: - 日志条目

struct BotLogEntry: View {
    let text: String

    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Text(text)
                .font(.system(.caption, design: .monospaced))
                .foregroundColor(logColor)
                .textSelection(.enabled)

            Spacer(minLength: 0)
        }
        .padding(.vertical, 1)
    }

    private var logColor: Color {
        if text.contains("❌") || text.contains("🔴") || text.contains("ERROR") {
            return .red
        } else if text.contains("⚠️") || text.contains("WARNING") {
            return .orange
        } else if text.contains("✅") || text.contains("🟢") || text.contains("SUCCESS") {
            return .green
        } else if text.contains("🚀") || text.contains("🔧") || text.contains("INFO") {
            return .blue
        } else {
            return .primary
        }
    }
}

#Preview {
    BotControlView()
}
